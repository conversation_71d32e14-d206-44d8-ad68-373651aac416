'use client'

import { useState } from 'react'
import apiService from '../services/api'

export default function AIChat() {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! I'm your AI assistant powered by AWS Bedrock. How can I help you today?",
      sender: 'ai',
      timestamp: new Date().toLocaleTimeString()
    }
  ])
  const [inputText, setInputText] = useState('')
  const [loading, setLoading] = useState(false)

  // Placeholder function for sending messages to AI
  const sendMessage = async () => {
    if (!inputText.trim()) return

    const userMessage = {
      id: Date.now(),
      text: inputText,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString()
    }

    setMessages(prev => [...prev, userMessage])
    setInputText('')
    setLoading(true)

    try {
      // TODO: Replace with actual AWS Bedrock API call
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputText,
          model: 'claude-3-sonnet', // AWS Bedrock model
        }),
      })

      let aiResponse
      if (response.ok) {
        const data = await response.json()
        aiResponse = data.response
      } else {
        // Fallback response when API is not connected
        aiResponse = `I received your message: "${inputText}". This is a placeholder response. Once AWS Bedrock is connected, I'll provide intelligent responses using Claude or other foundation models.`
      }

      const aiMessage = {
        id: Date.now() + 1,
        text: aiResponse,
        sender: 'ai',
        timestamp: new Date().toLocaleTimeString()
      }

      setMessages(prev => [...prev, aiMessage])
    } catch (error) {
      console.log('API not connected yet, using placeholder response')
      const aiMessage = {
        id: Date.now() + 1,
        text: `I received your message: "${inputText}". This is a placeholder response. Once AWS Bedrock is connected, I'll provide intelligent responses using Claude or other foundation models.`,
        sender: 'ai',
        timestamp: new Date().toLocaleTimeString()
      }
      setMessages(prev => [...prev, aiMessage])
    }

    setLoading(false)
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  return (
    <div className="card" style={{ height: '600px', display: 'flex', flexDirection: 'column' }}>
      <h2 style={{ marginBottom: '20px', color: '#333' }}>AI Chat - AWS Bedrock Integration</h2>
      
      {/* Chat Messages */}
      <div style={{ 
        flex: 1, 
        overflowY: 'auto', 
        border: '1px solid #e2e8f0', 
        borderRadius: '8px', 
        padding: '15px',
        marginBottom: '15px',
        backgroundColor: '#f8f9fa'
      }}>
        {messages.map((message) => (
          <div
            key={message.id}
            style={{
              marginBottom: '15px',
              display: 'flex',
              justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start'
            }}
          >
            <div
              style={{
                maxWidth: '70%',
                padding: '12px 16px',
                borderRadius: '18px',
                backgroundColor: message.sender === 'user' ? '#667eea' : '#ffffff',
                color: message.sender === 'user' ? 'white' : '#333',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}
            >
              <div style={{ marginBottom: '5px' }}>{message.text}</div>
              <div style={{ 
                fontSize: '0.75rem', 
                opacity: 0.7,
                textAlign: 'right'
              }}>
                {message.timestamp}
              </div>
            </div>
          </div>
        ))}
        
        {loading && (
          <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '15px' }}>
            <div style={{
              padding: '12px 16px',
              borderRadius: '18px',
              backgroundColor: '#ffffff',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <div className="loading"></div>
            </div>
          </div>
        )}
      </div>

      {/* Input Area */}
      <div style={{ display: 'flex', gap: '10px' }}>
        <textarea
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type your message here... (Press Enter to send)"
          style={{
            flex: 1,
            padding: '12px',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            resize: 'none',
            minHeight: '50px',
            fontFamily: 'inherit'
          }}
          disabled={loading}
        />
        <button
          onClick={sendMessage}
          disabled={loading || !inputText.trim()}
          className="button"
          style={{ alignSelf: 'flex-end', minWidth: '80px' }}
        >
          {loading ? <div className="loading"></div> : 'Send'}
        </button>
      </div>

      <div style={{ marginTop: '10px', fontSize: '0.9rem', color: '#666' }}>
        💡 This chat will be powered by AWS Bedrock (Claude, Llama, etc.) once backend is connected
      </div>
    </div>
  )
}
