// Frontend API Service Layer
// This handles all communication between React frontend and backend APIs

import axios from 'axios'

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'
const BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000/api'

// Create axios instances
const nextApiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

const backendApiClient = axios.create({
  baseURL: BACKEND_API_URL,
  timeout: 30000, // Longer timeout for AI operations
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptors for authentication
nextApiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

backendApiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptors for error handling
const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    console.error('API Error:', error.response.data)
    return Promise.reject(error.response.data)
  } else if (error.request) {
    // Request made but no response
    console.error('Network Error:', error.request)
    return Promise.reject({ message: 'Network error - please check your connection' })
  } else {
    // Something else happened
    console.error('Error:', error.message)
    return Promise.reject({ message: error.message })
  }
}

nextApiClient.interceptors.response.use(
  (response) => response,
  handleApiError
)

backendApiClient.interceptors.response.use(
  (response) => response,
  handleApiError
)

// API Service Functions
export const apiService = {
  // Dashboard APIs
  dashboard: {
    getStats: async () => {
      try {
        const response = await nextApiClient.get('/dashboard/stats')
        return response.data
      } catch (error) {
        // Fallback to backend API if Next.js API fails
        try {
          const response = await backendApiClient.get('/dashboard/stats')
          return response.data
        } catch (backendError) {
          throw error // Return original error
        }
      }
    },
    
    getServiceStatus: async () => {
      try {
        const response = await nextApiClient.get('/dashboard/services')
        return response.data
      } catch (error) {
        try {
          const response = await backendApiClient.get('/dashboard/services')
          return response.data
        } catch (backendError) {
          throw error
        }
      }
    }
  },

  // AI Chat APIs
  ai: {
    sendMessage: async (message, model = 'claude-3-sonnet') => {
      try {
        const response = await nextApiClient.post('/ai/chat', { message, model })
        return response.data
      } catch (error) {
        // Fallback to backend API
        try {
          const response = await backendApiClient.post('/ai/chat', { message, model })
          return response.data
        } catch (backendError) {
          throw error
        }
      }
    },
    
    getChatHistory: async (userId) => {
      try {
        const response = await nextApiClient.get(`/ai/history/${userId}`)
        return response.data
      } catch (error) {
        try {
          const response = await backendApiClient.get(`/ai/history/${userId}`)
          return response.data
        } catch (backendError) {
          throw error
        }
      }
    }
  },

  // User Management APIs
  user: {
    getProfile: async () => {
      try {
        const response = await nextApiClient.get('/user/profile')
        return response.data
      } catch (error) {
        try {
          const response = await backendApiClient.get('/user/profile')
          return response.data
        } catch (backendError) {
          throw error
        }
      }
    },
    
    updateProfile: async (profileData) => {
      try {
        const response = await nextApiClient.put('/user/profile', profileData)
        return response.data
      } catch (error) {
        try {
          const response = await backendApiClient.put('/user/profile', profileData)
          return response.data
        } catch (backendError) {
          throw error
        }
      }
    }
  },

  // AWS Services APIs
  aws: {
    invokeBedrockModel: async (modelId, prompt) => {
      try {
        const response = await nextApiClient.post('/aws/bedrock', { modelId, prompt })
        return response.data
      } catch (error) {
        try {
          const response = await backendApiClient.post('/aws/bedrock', { modelId, prompt })
          return response.data
        } catch (backendError) {
          throw error
        }
      }
    },
    
    invokeLambda: async (functionName, payload) => {
      try {
        const response = await nextApiClient.post('/aws/lambda', { functionName, payload })
        return response.data
      } catch (error) {
        try {
          const response = await backendApiClient.post('/aws/lambda', { functionName, payload })
          return response.data
        } catch (backendError) {
          throw error
        }
      }
    }
  }
}

export default apiService
