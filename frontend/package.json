{"name": "agent-alex-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"aws-sdk": "^2.1500.0", "next": "^15.3.3", "react": "^18", "react-dom": "^18", "axios": "^1.6.0", "dotenv": "^16.3.1", "lucide-react": "^0.294.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.0.0"}}