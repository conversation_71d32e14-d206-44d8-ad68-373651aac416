# Environment Configuration Template
# Copy this file to .env.local and fill in your actual values

# Application Settings
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
BACKEND_PORT=8000

# Next.js Public Variables (accessible in browser)
NEXT_PUBLIC_APP_NAME=Agent Alex
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000/api

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# AWS Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
BEDROCK_REGION=us-east-1

# AWS Lambda Functions
LAMBDA_AI_CHAT_FUNCTION=aiChatHandler
LAMBDA_DASHBOARD_FUNCTION=getDashboardStats
LAMBDA_USER_FUNCTION=userManagement

# DynamoDB Tables
DYNAMODB_USERS_TABLE=agent-alex-users
DYNAMODB_CONVERSATIONS_TABLE=agent-alex-conversations
DYNAMODB_ANALYTICS_TABLE=agent-alex-analytics

# S3 Buckets
S3_UPLOADS_BUCKET=agent-alex-uploads
S3_MODELS_BUCKET=agent-alex-models

# Authentication (for future implementation)
JWT_SECRET=your_jwt_secret_here
AUTH_PROVIDER=cognito
COGNITO_USER_POOL_ID=your_user_pool_id
COGNITO_CLIENT_ID=your_client_id

# Database (for future FastAPI migration)
DATABASE_URL=postgresql://user:password@localhost:5432/agent_alex
REDIS_URL=redis://localhost:6379

# Monitoring and Logging
LOG_LEVEL=info
SENTRY_DSN=your_sentry_dsn_here

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_REQUESTS_PER_HOUR=1000
