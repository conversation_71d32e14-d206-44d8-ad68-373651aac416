
import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Button } from '@/components/ui/button';
import { Logo } from '@/components/common/Logo';

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="bg-white shadow-sm z-10 relative">
        <div className="container-page py-4">
          <Logo />
        </div>
      </header>
      
      <div className="flex-grow flex items-center justify-center">
        <div className="text-center max-w-lg px-4">
          <div className="text-primary text-6xl font-bold mb-4">404</div>
          <h1 className="text-3xl font-bold text-gray-900 mb-3">Page Not Found</h1>
          <p className="text-gray-600 mb-8">
            We couldn't find the page you were looking for. It might have been moved or doesn't exist.
          </p>
          <div className="flex justify-center gap-4">
            <Button 
              className="bg-primary hover:bg-primary/90"
              onClick={() => window.history.back()}
            >
              Go Back
            </Button>
            <Button 
              variant="outline"
              onClick={() => window.location.href = '/'}
            >
              Go to Home
            </Button>
          </div>
        </div>
      </div>
      
      <footer className="bg-white border-t py-6">
        <div className="container-page">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <Logo />
            <div className="text-center md:text-right mt-4 md:mt-0">
              <p className="text-sm text-gray-500">© 2025 Alex Interviewer. All rights reserved.</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default NotFound;
