
import { Link } from 'react-router-dom';
import { PageHeader } from '@/components/common/PageHeader';
import { Button } from '@/components/ui/button';
import { 
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { mockAlexConfig } from '@/data/mockData';
import { Mail, Phone, User, Settings } from 'lucide-react';

const AlexConfig = () => {
  return (
    <div>
      <PageHeader 
        title="Alex Configuration"
        description="View and manage your AI agent settings"
        actions={
          <Link to="/onboarding">
            <Button className="bg-primary hover:bg-primary/90">
              <Settings className="mr-2 h-4 w-4" /> Edit Configuration
            </Button>
          </Link>
        }
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Mail className="mr-2 h-5 w-5 text-primary" /> Email Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Provider</span>
                <span className="text-sm">{mockAlexConfig.email.provider}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Email Address</span>
                <span className="text-sm">{mockAlexConfig.email.user}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Authentication</span>
                <span className="text-sm font-mono bg-gray-100 px-2 py-0.5 rounded text-xs">
                  {mockAlexConfig.email.token.slice(0, 5)}...{mockAlexConfig.email.token.slice(-5)}
                </span>
              </div>
              <div className="pt-2 mt-2 border-t">
                <p className="text-xs text-gray-500">
                  Alex uses this email account to contact candidates for scheduling interviews.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5 text-primary" /> Agent Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Agent Name</span>
                <span className="text-sm">{mockAlexConfig.agentName}</span>
              </div>
              <div className="pt-2 mt-2 border-t">
                <p className="text-xs text-gray-500">
                  This name is used when Alex introduces itself to candidates during interviews.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Phone className="mr-2 h-5 w-5 text-primary" /> Twilio Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Phone Number</span>
                <span className="text-sm">{mockAlexConfig.twilioNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Assigned Date</span>
                <span className="text-sm">{mockAlexConfig.assignedDate}</span>
              </div>
              <div className="pt-2 mt-2 border-t">
                <p className="text-xs text-gray-500">
                  This phone number is used by Alex to call candidates. It is managed by Alex Interviewer and cannot be changed.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div className="mt-6">
        <Card className="bg-blue-50 border-blue-100">
          <CardContent className="p-4">
            <div className="flex items-start">
              <div className="bg-blue-100 rounded-full p-2 mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-medium text-blue-800 mb-1">Need to change your configuration?</h3>
                <p className="text-blue-700">
                  You can update your email provider, agent name, and other settings through the onboarding process.
                </p>
                <Link to="/onboarding" className="mt-4 inline-block">
                  <Button className="bg-blue-700 hover:bg-blue-800">
                    Go to Onboarding
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AlexConfig;
