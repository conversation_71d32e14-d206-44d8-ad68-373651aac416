
import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { PageHeader } from '@/components/common/PageHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Edit, Trash2, Search, Plus } from 'lucide-react';
import { mockJobRoles, mockCandidates } from '@/data/mockData';

const JobRoles = () => {
  const [searchTerm, setSearchTerm] = useState('');
  
  const filteredRoles = mockJobRoles.filter(role => 
    role.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const getCandidateCount = (roleId) => {
    return mockCandidates.filter(candidate => candidate.jobRoleId === roleId).length;
  };

  return (
    <div>
      <PageHeader 
        title="Job Roles"
        description="Create and manage job roles for your interviews"
        actions={
          <Link to="/job-roles/create">
            <Button className="bg-primary hover:bg-primary/90">
              <Plus className="mr-2 h-4 w-4" /> New Job Role
            </Button>
          </Link>
        }
      />
      
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search job roles..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>
      
      {filteredRoles.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900">No job roles found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Create a new job role to get started.
          </p>
          <div className="mt-6">
            <Link to="/job-roles/create">
              <Button className="bg-primary hover:bg-primary/90">
                <Plus className="mr-2 h-4 w-4" /> New Job Role
              </Button>
            </Link>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRoles.map((role) => (
            <Card key={role.id} className="overflow-hidden">
              <CardHeader className="pb-3">
                <CardTitle className="text-xl">{role.name}</CardTitle>
                <CardDescription className="text-sm text-gray-500">
                  Created on {new Date(role.createdAt).toLocaleDateString()}
                </CardDescription>
              </CardHeader>
              <CardContent className="pb-3">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Key Skills</h4>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {role.skills.slice(0, 3).map((skill, i) => (
                        <span key={i} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {skill}
                        </span>
                      ))}
                      {role.skills.length > 3 && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          +{role.skills.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Experience Required</h4>
                    <p className="mt-1 text-sm">{role.experience} years</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Candidates</h4>
                    <p className="mt-1 text-sm">{getCandidateCount(role.id)} candidates</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between pt-3 border-t">
                <Link to={`/candidates?role=${role.id}`}>
                  <Button variant="outline" size="sm">
                    View Candidates
                  </Button>
                </Link>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="icon">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default JobRoles;
