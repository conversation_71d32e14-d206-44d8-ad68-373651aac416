
import { cn } from "@/lib/utils";

export function StatusBadge({ status, className }) {
  const getStatusColor = () => {
    switch (status) {
      case 'Scheduled':
      case 'Active':
        return 'bg-blue-100 text-blue-800';
      case 'In Progress':
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Completed':
      case 'Resolved':
      case 'Pass':
        return 'bg-green-100 text-green-800';
      case 'Cancelled':
      case 'Fail':
        return 'bg-red-100 text-red-800';
      case 'N/A':
      case 'Not Scheduled':
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <span className={cn(
      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
      getStatusColor(),
      className
    )}>
      {status}
    </span>
  );
}
