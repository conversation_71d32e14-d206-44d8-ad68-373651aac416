
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { ProtectedRoute } from "./components/common/ProtectedRoute";
import { AdminRoute } from "./components/common/AdminRoute";
import { UserLayout } from "./components/layouts/UserLayout";
import { AdminLayout } from "./components/layouts/AdminLayout";

// Public pages
import HomePage from "./pages/HomePage";
import NotFound from "./pages/NotFound";

// User pages
import Dashboard from "./pages/user/Dashboard";
import JobRoles from "./pages/user/JobRoles";
import JobRoleCreate from "./pages/user/JobRoleCreate";
import Candidates from "./pages/user/Candidates";
import CandidateProfile from "./pages/user/CandidateProfile";
import CandidateQuestions from "./pages/user/CandidateQuestions";
import Interviews from "./pages/user/Interviews";
import ActivityLog from "./pages/user/ActivityLog";
import AlexConfig from "./pages/user/AlexConfig";
import Onboarding from "./pages/user/Onboarding";

// Admin pages
import AdminDashboard from "./pages/admin/AdminDashboard";
import UserSubscriptions from "./pages/admin/UserSubscriptions";
import AccountManagement from "./pages/admin/AccountManagement";
import TwilioNumbers from "./pages/admin/TwilioNumbers";
import Issues from "./pages/admin/Issues";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<HomePage />} />

            {/* User routes */}
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <UserLayout>
                  <Dashboard />
                </UserLayout>
              </ProtectedRoute>
            } />
            <Route path="/job-roles" element={
              <ProtectedRoute>
                <UserLayout>
                  <JobRoles />
                </UserLayout>
              </ProtectedRoute>
            } />
            <Route path="/job-roles/create" element={
              <ProtectedRoute>
                <UserLayout>
                  <JobRoleCreate />
                </UserLayout>
              </ProtectedRoute>
            } />
            <Route path="/candidates" element={
              <ProtectedRoute>
                <UserLayout>
                  <Candidates />
                </UserLayout>
              </ProtectedRoute>
            } />
            <Route path="/candidates/:id" element={
              <ProtectedRoute>
                <UserLayout>
                  <CandidateProfile />
                </UserLayout>
              </ProtectedRoute>
            } />
            <Route path="/candidates/:id/questions" element={
              <ProtectedRoute>
                <UserLayout>
                  <CandidateQuestions />
                </UserLayout>
              </ProtectedRoute>
            } />
            <Route path="/interviews" element={
              <ProtectedRoute>
                <UserLayout>
                  <Interviews />
                </UserLayout>
              </ProtectedRoute>
            } />
            <Route path="/activity" element={
              <ProtectedRoute>
                <UserLayout>
                  <ActivityLog />
                </UserLayout>
              </ProtectedRoute>
            } />
            <Route path="/config" element={
              <ProtectedRoute>
                <UserLayout>
                  <AlexConfig />
                </UserLayout>
              </ProtectedRoute>
            } />
            <Route path="/onboarding" element={
              <ProtectedRoute>
                <Onboarding />
              </ProtectedRoute>
            } />

            {/* Admin routes */}
            <Route path="/admin/dashboard" element={
              <AdminRoute>
                <AdminLayout>
                  <AdminDashboard />
                </AdminLayout>
              </AdminRoute>
            } />
            <Route path="/admin/users" element={
              <AdminRoute>
                <AdminLayout>
                  <UserSubscriptions />
                </AdminLayout>
              </AdminRoute>
            } />
            <Route path="/admin/accounts" element={
              <AdminRoute>
                <AdminLayout>
                  <AccountManagement />
                </AdminLayout>
              </AdminRoute>
            } />
            <Route path="/admin/twilio" element={
              <AdminRoute>
                <AdminLayout>
                  <TwilioNumbers />
                </AdminLayout>
              </AdminRoute>
            } />
            <Route path="/admin/issues" element={
              <AdminRoute>
                <AdminLayout>
                  <Issues />
                </AdminLayout>
              </AdminRoute>
            } />

            {/* 404 route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
