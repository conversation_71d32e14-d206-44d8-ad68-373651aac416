// AI Service - Business Logic for AI Operations
const awsService = require('./awsService')

class AIService {
  constructor() {
    this.activeAgents = new Map()
    this.conversationHistory = new Map()
  }

  async processMessage(message, model = 'claude-3-sonnet', userId = 'anonymous') {
    try {
      // Store user message
      this.addToHistory(userId, {
        role: 'user',
        content: message,
        timestamp: new Date().toISOString()
      })

      // Process with AWS Bedrock
      const response = await this.invokeAIModel(model, message, userId)
      
      // Store AI response
      this.addToHistory(userId, {
        role: 'assistant',
        content: response.content,
        timestamp: new Date().toISOString(),
        model: model,
        metadata: response.metadata
      })

      return {
        response: response.content,
        model: model,
        timestamp: new Date().toISOString(),
        conversationId: this.getConversationId(userId),
        metadata: response.metadata
      }
    } catch (error) {
      console.error('AI processing error:', error)
      return this.getFallbackResponse(message, model)
    }
  }

  async invokeAIModel(model, message, userId) {
    try {
      // Try AWS Bedrock first
      const bedrockResponse = await awsService.invokeBedrockModel(
        this.getBedrockModelId(model),
        message,
        {
          max_tokens: 1000,
          temperature: 0.7,
          top_p: 0.9
        }
      )

      return {
        content: bedrockResponse.content,
        metadata: {
          model: model,
          tokens_used: bedrockResponse.usage?.total_tokens || 0,
          processing_time: bedrockResponse.processing_time || 0
        }
      }
    } catch (error) {
      console.error('Bedrock invocation failed:', error)
      
      // Fallback to Lambda function
      try {
        const lambdaResponse = await awsService.invokeLambda('aiChatHandler', {
          message,
          model,
          userId
        })

        return {
          content: lambdaResponse.response,
          metadata: {
            model: model,
            fallback: 'lambda',
            processing_time: lambdaResponse.processing_time || 0
          }
        }
      } catch (lambdaError) {
        console.error('Lambda fallback failed:', lambdaError)
        throw new Error('All AI services unavailable')
      }
    }
  }

  async getChatHistory(userId, limit = 50, offset = 0) {
    try {
      // TODO: Implement DynamoDB query for persistent storage
      const history = this.conversationHistory.get(userId) || []
      
      return {
        conversations: history.slice(offset, offset + limit),
        total: history.length,
        userId: userId,
        limit: limit,
        offset: offset
      }
    } catch (error) {
      console.error('Chat history error:', error)
      return {
        conversations: [],
        total: 0,
        userId: userId,
        error: error.message
      }
    }
  }

  async getAvailableModels() {
    try {
      // TODO: Query AWS Bedrock for available models
      return {
        models: [
          {
            id: 'claude-3-sonnet',
            name: 'Claude 3 Sonnet',
            provider: 'Anthropic',
            description: 'Most capable model for complex tasks',
            maxTokens: 200000,
            pricing: { input: 0.003, output: 0.015 }
          },
          {
            id: 'claude-3-haiku',
            name: 'Claude 3 Haiku',
            provider: 'Anthropic',
            description: 'Fastest model for simple tasks',
            maxTokens: 200000,
            pricing: { input: 0.00025, output: 0.00125 }
          },
          {
            id: 'llama-2-70b',
            name: 'Llama 2 70B',
            provider: 'Meta',
            description: 'Open source model for general tasks',
            maxTokens: 4096,
            pricing: { input: 0.001, output: 0.002 }
          }
        ],
        lastUpdated: new Date().toISOString()
      }
    } catch (error) {
      console.error('Models fetch error:', error)
      throw error
    }
  }

  async deployAgent(agentConfig) {
    try {
      const agentId = this.generateAgentId()
      
      // TODO: Deploy to AWS Lambda or ECS
      const deployment = {
        agentId: agentId,
        name: agentConfig.name,
        model: agentConfig.model,
        instructions: agentConfig.instructions,
        status: 'deploying',
        createdAt: new Date().toISOString()
      }

      this.activeAgents.set(agentId, deployment)

      // Simulate deployment process
      setTimeout(() => {
        const agent = this.activeAgents.get(agentId)
        if (agent) {
          agent.status = 'active'
          agent.deployedAt = new Date().toISOString()
        }
      }, 3000)

      return deployment
    } catch (error) {
      console.error('Agent deployment error:', error)
      throw error
    }
  }

  async getActiveAgents() {
    try {
      const agents = Array.from(this.activeAgents.values())
      return {
        agents: agents,
        total: agents.length,
        active: agents.filter(a => a.status === 'active').length,
        deploying: agents.filter(a => a.status === 'deploying').length
      }
    } catch (error) {
      console.error('Active agents error:', error)
      throw error
    }
  }

  async stopAgent(agentId) {
    try {
      const agent = this.activeAgents.get(agentId)
      if (!agent) {
        throw new Error('Agent not found')
      }

      agent.status = 'stopping'
      
      // TODO: Stop AWS Lambda or ECS service
      setTimeout(() => {
        this.activeAgents.delete(agentId)
      }, 2000)

      return { message: 'Agent stop initiated', agentId }
    } catch (error) {
      console.error('Stop agent error:', error)
      throw error
    }
  }

  // Helper methods
  addToHistory(userId, message) {
    if (!this.conversationHistory.has(userId)) {
      this.conversationHistory.set(userId, [])
    }
    
    const history = this.conversationHistory.get(userId)
    history.push(message)
    
    // Keep only last 100 messages in memory
    if (history.length > 100) {
      history.splice(0, history.length - 100)
    }
  }

  getConversationId(userId) {
    return `conv_${userId}_${Date.now()}`
  }

  getBedrockModelId(model) {
    const modelMap = {
      'claude-3-sonnet': 'anthropic.claude-3-sonnet-20240229-v1:0',
      'claude-3-haiku': 'anthropic.claude-3-haiku-20240307-v1:0',
      'llama-2-70b': 'meta.llama2-70b-chat-v1'
    }
    
    return modelMap[model] || modelMap['claude-3-sonnet']
  }

  generateAgentId() {
    return `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  getFallbackResponse(message, model) {
    const responses = [
      `I received your message: "${message}". I'm currently running in demo mode with ${model}. Once AWS Bedrock is fully connected, I'll provide intelligent responses.`,
      `Thank you for your message: "${message}". This is a placeholder response. The real integration will use AWS Bedrock's ${model} model.`,
      `I understand you said: "${message}". Currently in development mode. When connected to AWS Bedrock, I'll use ${model} for sophisticated responses.`
    ]

    return {
      response: responses[Math.floor(Math.random() * responses.length)],
      model: model,
      timestamp: new Date().toISOString(),
      fallback: true,
      metadata: {
        model: model,
        fallback_reason: 'AWS services unavailable'
      }
    }
  }
}

module.exports = new AIService()
