// User Management API Routes
const express = require('express')
const router = express.Router()
const userService = require('../services/userService')

// GET /api/user/profile
router.get('/profile', async (req, res) => {
  try {
    // TODO: Get user ID from JWT token
    const userId = req.headers['x-user-id'] || 'demo-user'
    const profile = await userService.getProfile(userId)
    res.json(profile)
  } catch (error) {
    console.error('Get profile error:', error)
    res.status(500).json({
      error: 'Failed to fetch user profile',
      message: error.message
    })
  }
})

// PUT /api/user/profile
router.put('/profile', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'] || 'demo-user'
    const profileData = req.body
    
    const updatedProfile = await userService.updateProfile(userId, profileData)
    res.json(updatedProfile)
  } catch (error) {
    console.error('Update profile error:', error)
    res.status(500).json({
      error: 'Failed to update user profile',
      message: error.message
    })
  }
})

// GET /api/user/preferences
router.get('/preferences', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'] || 'demo-user'
    const preferences = await userService.getPreferences(userId)
    res.json(preferences)
  } catch (error) {
    console.error('Get preferences error:', error)
    res.status(500).json({
      error: 'Failed to fetch user preferences',
      message: error.message
    })
  }
})

// PUT /api/user/preferences
router.put('/preferences', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'] || 'demo-user'
    const preferences = req.body
    
    const updatedPreferences = await userService.updatePreferences(userId, preferences)
    res.json(updatedPreferences)
  } catch (error) {
    console.error('Update preferences error:', error)
    res.status(500).json({
      error: 'Failed to update user preferences',
      message: error.message
    })
  }
})

// GET /api/user/usage
router.get('/usage', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'] || 'demo-user'
    const { timeRange = '30d' } = req.query
    
    const usage = await userService.getUsageStats(userId, timeRange)
    res.json(usage)
  } catch (error) {
    console.error('Get usage error:', error)
    res.status(500).json({
      error: 'Failed to fetch usage statistics',
      message: error.message
    })
  }
})

module.exports = router
