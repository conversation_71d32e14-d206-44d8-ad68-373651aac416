// AWS Services API Routes
const express = require('express')
const router = express.Router()
const awsService = require('../services/awsService')

// POST /api/aws/bedrock
router.post('/bedrock', async (req, res) => {
  try {
    const { modelId, prompt, parameters = {} } = req.body
    
    if (!modelId || !prompt) {
      return res.status(400).json({
        error: 'Model ID and prompt are required'
      })
    }

    const response = await awsService.invokeBedrockModel(modelId, prompt, parameters)
    res.json(response)
  } catch (error) {
    console.error('Bedrock error:', error)
    res.status(500).json({
      error: 'Failed to invoke Bedrock model',
      message: error.message
    })
  }
})

// POST /api/aws/lambda
router.post('/lambda', async (req, res) => {
  try {
    const { functionName, payload } = req.body
    
    if (!functionName) {
      return res.status(400).json({
        error: 'Function name is required'
      })
    }

    const response = await awsService.invokeLambda(functionName, payload)
    res.json(response)
  } catch (error) {
    console.error('Lambda error:', error)
    res.status(500).json({
      error: 'Failed to invoke Lambda function',
      message: error.message
    })
  }
})

// GET /api/aws/services/status
router.get('/services/status', async (req, res) => {
  try {
    const status = await awsService.getServicesStatus()
    res.json(status)
  } catch (error) {
    console.error('AWS services status error:', error)
    res.status(500).json({
      error: 'Failed to fetch AWS services status',
      message: error.message
    })
  }
})

// POST /api/aws/s3/upload
router.post('/s3/upload', async (req, res) => {
  try {
    const { fileName, fileContent, bucket } = req.body
    
    if (!fileName || !fileContent) {
      return res.status(400).json({
        error: 'File name and content are required'
      })
    }

    const uploadResult = await awsService.uploadToS3(fileName, fileContent, bucket)
    res.json(uploadResult)
  } catch (error) {
    console.error('S3 upload error:', error)
    res.status(500).json({
      error: 'Failed to upload to S3',
      message: error.message
    })
  }
})

// GET /api/aws/dynamodb/:table
router.get('/dynamodb/:table', async (req, res) => {
  try {
    const { table } = req.params
    const { key, limit = 10 } = req.query
    
    const data = await awsService.queryDynamoDB(table, key, parseInt(limit))
    res.json(data)
  } catch (error) {
    console.error('DynamoDB query error:', error)
    res.status(500).json({
      error: 'Failed to query DynamoDB',
      message: error.message
    })
  }
})

// POST /api/aws/dynamodb/:table
router.post('/dynamodb/:table', async (req, res) => {
  try {
    const { table } = req.params
    const item = req.body
    
    const result = await awsService.putDynamoDBItem(table, item)
    res.json(result)
  } catch (error) {
    console.error('DynamoDB put error:', error)
    res.status(500).json({
      error: 'Failed to put item in DynamoDB',
      message: error.message
    })
  }
})

module.exports = router
