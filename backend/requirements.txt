# FastAPI Backend Dependencies

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0

# AWS SDK
boto3==1.34.0
botocore==1.34.0

# HTTP and CORS
httpx==0.25.2
python-multipart==0.0.6

# Environment and Configuration
python-dotenv==1.0.0
pydantic-settings==2.1.0

# Database (for future scaling)
sqlalchemy==2.0.23
alembic==1.13.1
asyncpg==0.29.0

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Caching and Performance
redis==5.0.1
aioredis==2.0.1

# Monitoring and Logging
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Data Processing
pandas==2.1.4
numpy==1.25.2

# Date and Time
python-dateutil==2.8.2
