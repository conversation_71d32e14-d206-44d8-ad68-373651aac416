
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { PageHeader } from '@/components/common/PageHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { mockActivities } from '@/data/mockData';
import { Search, Download, Mail, Phone, Calendar, FileText } from 'lucide-react';

const ActivityLog = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  
  const filteredActivities = mockActivities.filter(activity => {
    const matchesSearch = 
      activity.candidateName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.details.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesAction = actionFilter === 'all' || activity.action === actionFilter;
    
    // Very simple date filtering for demo
    const matchesDate = dateFilter === 'all' || 
      (dateFilter === 'today' && activity.timestamp.includes('2025-05-22')) ||
      (dateFilter === 'yesterday' && activity.timestamp.includes('2025-05-21')) ||
      (dateFilter === 'thisWeek' && (
        activity.timestamp.includes('2025-05-22') || 
        activity.timestamp.includes('2025-05-21') || 
        activity.timestamp.includes('2025-05-20') ||
        activity.timestamp.includes('2025-05-19')
      ));
    
    return matchesSearch && matchesAction && matchesDate;
  });
  
  const getActionIcon = (action) => {
    switch(action) {
      case 'Emailed': return <Mail className="h-4 w-4 text-blue-500" />;
      case 'Called': return <Phone className="h-4 w-4 text-green-500" />;
      case 'Scheduled Interview': return <Calendar className="h-4 w-4 text-purple-500" />;
      case 'Interview Completed': return <FileText className="h-4 w-4 text-amber-500" />;
      default: return <Mail className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div>
      <PageHeader 
        title="Agent Activity Log"
        description="Track all actions performed by Alex"
        actions={
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" /> Export CSV
          </Button>
        }
      />
      
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search by candidate or details..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4 sm:w-auto">
          <Select
            value={actionFilter}
            onValueChange={setActionFilter}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="All Actions" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Actions</SelectItem>
              <SelectItem value="Emailed">Emailed</SelectItem>
              <SelectItem value="Called">Called</SelectItem>
              <SelectItem value="Scheduled Interview">Scheduled</SelectItem>
              <SelectItem value="Interview Completed">Completed</SelectItem>
            </SelectContent>
          </Select>
          
          <Select
            value={dateFilter}
            onValueChange={setDateFilter}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="All Dates" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Dates</SelectItem>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="yesterday">Yesterday</SelectItem>
              <SelectItem value="thisWeek">This Week</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="bg-white overflow-hidden shadow-sm rounded-lg">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 text-left">
              <tr>
                <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Candidate</th>
                <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">View</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredActivities.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-10 text-center text-gray-500">
                    No activities found. Try adjusting your search or filters.
                  </td>
                </tr>
              ) : (
                filteredActivities.map((activity) => (
                  <tr key={activity.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {activity.timestamp}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getActionIcon(activity.action)}
                        <span className="ml-2 text-sm font-medium">{activity.action}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {activity.candidateName ? (
                        <Link to={`/candidates/${activity.candidateId}`} className="text-primary hover:underline">
                          {activity.candidateName}
                        </Link>
                      ) : (
                        <span className="text-gray-500">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 max-w-md truncate">
                      {activity.details}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {activity.candidateId && (
                        <Link to={`/candidates/${activity.candidateId}`}>
                          <Button variant="ghost" size="sm">View</Button>
                        </Link>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ActivityLog;
