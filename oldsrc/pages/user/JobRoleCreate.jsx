
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { PageHeader } from '@/components/common/PageHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Card, 
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Upload, X } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const JobRoleCreate = () => {
  const [formData, setFormData] = useState({
    name: '',
    duties: '',
    skills: '',
    education: '',
    experience: '',
    criteria: '',
  });
  const [uploadedFile, setUploadedFile] = useState(null);
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setUploadedFile(e.target.files[0]);
      
      // Simulate AI extraction with sample data
      setTimeout(() => {
        setFormData({
          name: 'Software Engineer',
          duties: 'Develop web applications using modern technologies. Collaborate with cross-functional teams to define and design new features.',
          skills: 'Python, JavaScript, React, TypeScript, Node.js',
          education: 'BS Computer Science or equivalent',
          experience: '3',
          criteria: 'Strong Python skills, 3+ years experience, knowledge of React',
        });
        
        toast({
          title: "Job Description Processed",
          description: "AI has extracted job details from the uploaded file",
        });
      }, 1500);
    }
  };
  
  const handleRemoveFile = () => {
    setUploadedFile(null);
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    toast({
      title: "Job Role Created",
      description: `${formData.name} has been created successfully`,
    });
    
    navigate('/job-roles');
  };

  return (
    <div>
      <PageHeader 
        title="Create Job Role"
        description="Add a new job role for interviewing candidates"
      />
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>How would you like to create this job role?</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="manual" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="manual">Manual Entry</TabsTrigger>
              <TabsTrigger value="upload">Upload JD</TabsTrigger>
            </TabsList>
            
            <TabsContent value="manual" className="mt-6">
              <p className="text-sm text-gray-500 mb-4">
                Enter the job details manually using the form below
              </p>
            </TabsContent>
            
            <TabsContent value="upload" className="mt-6">
              <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                {!uploadedFile ? (
                  <div className="text-center">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-600">
                      Upload a job description document (PDF, DOCX)
                    </p>
                    <p className="mt-1 text-xs text-gray-500">
                      AI will extract job details automatically
                    </p>
                    <label htmlFor="file-upload" className="mt-4 inline-block">
                      <Input
                        id="file-upload"
                        type="file"
                        className="hidden"
                        accept=".pdf,.docx,.doc"
                        onChange={handleFileChange}
                      />
                      <Button type="button" className="bg-primary hover:bg-primary/90">
                        Select File
                      </Button>
                    </label>
                  </div>
                ) : (
                  <div className="w-full">
                    <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
                      <div className="flex items-center">
                        <div className="bg-blue-100 rounded p-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">{uploadedFile.name}</p>
                          <p className="text-xs text-gray-500">{Math.round(uploadedFile.size / 1024)} KB</p>
                        </div>
                      </div>
                      <button
                        type="button"
                        className="text-gray-500 hover:text-gray-700"
                        onClick={handleRemoveFile}
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                    <p className="mt-4 text-sm text-gray-600">
                      AI has extracted job details. Review and edit below as needed.
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Job Role Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Job Role Name</Label>
              <Input
                id="name"
                name="name"
                placeholder="e.g. Software Engineer"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="duties">Job Duties</Label>
              <Textarea
                id="duties"
                name="duties"
                placeholder="Describe the main responsibilities of this role"
                value={formData.duties}
                onChange={handleChange}
                rows={4}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="skills">Skills (comma-separated)</Label>
              <Input
                id="skills"
                name="skills"
                placeholder="e.g. Python, JavaScript, Communication"
                value={formData.skills}
                onChange={handleChange}
                required
              />
              <p className="text-xs text-gray-500">Enter both technical and soft skills</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="education">Education Qualifications</Label>
                <Input
                  id="education"
                  name="education"
                  placeholder="e.g. BS Computer Science"
                  value={formData.education}
                  onChange={handleChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="experience">Experience (years)</Label>
                <Input
                  id="experience"
                  name="experience"
                  type="number"
                  placeholder="e.g. 3"
                  value={formData.experience}
                  onChange={handleChange}
                  min="0"
                  required
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="criteria">Criteria for Best Match</Label>
              <Textarea
                id="criteria"
                name="criteria"
                placeholder="What makes a candidate ideal for this role?"
                value={formData.criteria}
                onChange={handleChange}
                rows={3}
                required
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={() => navigate('/job-roles')}>
              Cancel
            </Button>
            <Button type="submit" className="bg-primary hover:bg-primary/90">
              Create Job Role
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
};

export default JobRoleCreate;
