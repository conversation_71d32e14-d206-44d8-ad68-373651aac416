
import { Link } from 'react-router-dom';
import { PageHeader } from '@/components/common/PageHeader';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { mockCandidates, mockJobRoles } from '@/data/mockData';
import { useAuth } from '@/contexts/AuthContext';

const Dashboard = () => {
  const { user } = useAuth();
  
  // Calculate metrics
  const totalCandidates = mockCandidates.length;
  const screenedCandidates = mockCandidates.filter(c => c.interviewStatus === 'Completed').length;
  const passedCandidates = mockCandidates.filter(c => c.interviewResult === 'Pass').length;
  const failedCandidates = mockCandidates.filter(c => c.interviewResult === 'Fail').length;
  
  // Subscription data
  const callsUsed = 50;
  const callsTotal = 120;
  const callsPercentage = Math.round((callsUsed / callsTotal) * 100);
  
  // Recent invoices
  const recentInvoices = [
    { invoiceNumber: 'INV001', amount: '$50.00', status: 'Paid', date: '2025-05-01' },
    { invoiceNumber: 'INV003', amount: '$150.00', status: 'Paid', date: '2025-05-10' },
  ];

  return (
    <div>
      <PageHeader 
        title={`Welcome, ${user?.email.split('@')[0]}`}
        description="Here's an overview of your account"
        actions={
          <Button className="bg-primary hover:bg-primary/90">
            Manage Subscription
          </Button>
        }
      />
      
      {/* Subscription ending alert */}
      <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-md text-amber-800 flex items-center justify-between">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <span>Your Basic subscription is ending on 2025-05-31.</span>
        </div>
        <Button variant="ghost" size="sm" className="text-amber-800">
          Renew Now
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Usage Card */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">Usage</h3>
          <div className="mb-2 flex justify-between items-center">
            <span className="text-sm text-gray-500">Basic Plan</span>
            <span className="text-sm font-medium">{callsUsed}/{callsTotal} calls</span>
          </div>
          <Progress value={callsPercentage} className="h-2 mb-4" />
          <div className="mb-2 flex justify-between items-center">
            <span className="text-sm text-gray-500">Vouchers</span>
            <span className="text-sm font-medium">3/5 remaining</span>
          </div>
          <Progress value={40} className="h-2" />
        </Card>
        
        {/* Job Roles Card */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">Job Roles</h3>
          <div className="space-y-3">
            {mockJobRoles.slice(0, 3).map(role => (
              <div key={role.id} className="flex justify-between items-center">
                <span>{role.name}</span>
                <span className="text-sm text-gray-500">{mockCandidates.filter(c => c.jobRoleId === role.id).length} candidates</span>
              </div>
            ))}
          </div>
          <div className="mt-4">
            <Link to="/job-roles">
              <Button variant="ghost" size="sm" className="w-full">View All Job Roles</Button>
            </Link>
          </div>
        </Card>
        
        {/* Metrics Card */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">Candidate Metrics</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded-md">
              <div className="text-2xl font-semibold text-blue-700">{totalCandidates}</div>
              <div className="text-sm text-gray-500">Total Candidates</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-md">
              <div className="text-2xl font-semibold text-purple-700">{screenedCandidates}</div>
              <div className="text-sm text-gray-500">Screened</div>
            </div>
            <div className="bg-green-50 p-4 rounded-md">
              <div className="text-2xl font-semibold text-green-700">{passedCandidates}</div>
              <div className="text-sm text-gray-500">Passed</div>
            </div>
            <div className="bg-red-50 p-4 rounded-md">
              <div className="text-2xl font-semibold text-red-700">{failedCandidates}</div>
              <div className="text-sm text-gray-500">Failed</div>
            </div>
          </div>
        </Card>
      </div>
      
      {/* Recent Billing */}
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-4">Recent Billing</h3>
        <div className="bg-white overflow-hidden shadow-sm rounded-lg">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 text-left">
                <tr>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {recentInvoices.map((invoice, i) => (
                  <tr key={i} className="bg-white">
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">{invoice.invoiceNumber}</td>
                    <td className="px-6 py-4 text-sm text-gray-500">{invoice.amount}</td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {invoice.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">{invoice.date}</td>
                    <td className="px-6 py-4 text-sm">
                      <Button variant="ghost" size="sm">View</Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      {/* Upcoming Interviews */}
      <div>
        <h3 className="text-lg font-medium mb-4">Upcoming Interviews</h3>
        <div className="bg-white overflow-hidden shadow-sm rounded-lg">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 text-left">
                <tr>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Candidate</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {mockCandidates
                  .filter(c => c.interviewStatus === 'Scheduled')
                  .map((candidate) => {
                    const jobRole = mockJobRoles.find(r => r.id === candidate.jobRoleId);
                    return (
                      <tr key={candidate.id} className="bg-white">
                        <td className="px-6 py-4 text-sm font-medium text-gray-900">{candidate.name}</td>
                        <td className="px-6 py-4 text-sm text-gray-500">{jobRole?.name || 'Unknown'}</td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {candidate.scheduleDateTime ? new Date(candidate.scheduleDateTime).toLocaleString() : 'Not scheduled'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {candidate.interviewStatus}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm">
                          <Link to={`/candidates/${candidate.id}`}>
                            <Button variant="ghost" size="sm">View</Button>
                          </Link>
                        </td>
                      </tr>
                    );
                  })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
