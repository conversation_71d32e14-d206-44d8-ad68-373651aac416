
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { PageHeader } from '@/components/common/PageHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { StatusBadge } from '@/components/common/StatusBadge';
import { mockCandidates, mockJobRoles } from '@/data/mockData';
import { Search, Download, Calendar, X } from 'lucide-react';

const Interviews = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [selectedInterviews, setSelectedInterviews] = useState([]);
  
  const interviewCandidates = mockCandidates.filter(candidate => {
    const matchesSearch = candidate.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || candidate.interviewStatus === statusFilter;
    const matchesRole = roleFilter === 'all' || candidate.jobRoleId === roleFilter;
    
    return matchesSearch && matchesStatus && matchesRole;
  });
  
  const getJobRole = (jobRoleId) => {
    const role = mockJobRoles.find(role => role.id === jobRoleId);
    return role ? role.name : 'Unknown';
  };
  
  const toggleSelectInterview = (id) => {
    setSelectedInterviews(prev => 
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  };
  
  const selectAll = () => {
    if (selectedInterviews.length === interviewCandidates.length) {
      setSelectedInterviews([]);
    } else {
      setSelectedInterviews(interviewCandidates.map(c => c.id));
    }
  };

  return (
    <div>
      <PageHeader 
        title="Interviews"
        description="Track and manage candidate interviews"
        actions={
          <Button className="bg-primary hover:bg-primary/90">
            <Calendar className="mr-2 h-4 w-4" /> Schedule Interview
          </Button>
        }
      />
      
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search candidate name..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 sm:w-auto">
          <Select
            value={roleFilter}
            onValueChange={setRoleFilter}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="All Roles" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              {mockJobRoles.map(role => (
                <SelectItem key={role.id} value={role.id}>{role.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select
            value={statusFilter}
            onValueChange={setStatusFilter}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="Scheduled">Scheduled</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
              <SelectItem value="Not Scheduled">Not Scheduled</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" /> Export CSV
          </Button>
        </div>
      </div>
      
      {selectedInterviews.length > 0 && (
        <div className="mb-4 p-2 bg-blue-50 border border-blue-100 rounded flex items-center justify-between">
          <span className="text-sm">
            {selectedInterviews.length} {selectedInterviews.length === 1 ? 'interview' : 'interviews'} selected
          </span>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="bg-white">
              Reschedule
            </Button>
            <Button variant="outline" size="sm" className="bg-white text-red-500 hover:text-red-600">
              Cancel Interviews
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setSelectedInterviews([])}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
      
      <div className="bg-white overflow-hidden shadow-sm rounded-lg">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 text-left">
              <tr>
                <th className="px-4 py-3 text-xs font-medium text-gray-500">
                  <input 
                    type="checkbox"
                    className="rounded text-primary focus:ring-primary"
                    checked={selectedInterviews.length === interviewCandidates.length && interviewCandidates.length > 0}
                    onChange={selectAll}
                  />
                </th>
                <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Candidate</th>
                <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {interviewCandidates.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-10 text-center text-gray-500">
                    No interviews found. Try adjusting your search or filters.
                  </td>
                </tr>
              ) : (
                interviewCandidates.map((candidate) => (
                  <tr key={candidate.id} className={`hover:bg-gray-50 ${selectedInterviews.includes(candidate.id) ? 'bg-blue-50' : ''}`}>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <input 
                        type="checkbox"
                        className="rounded text-primary focus:ring-primary"
                        checked={selectedInterviews.includes(candidate.id)}
                        onChange={() => toggleSelectInterview(candidate.id)}
                      />
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-full bg-primary/20 flex items-center justify-center text-primary font-medium">
                          {candidate.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">{candidate.name}</div>
                          <div className="text-xs text-gray-500">{candidate.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getJobRole(candidate.jobRoleId)}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      {candidate.scheduleDateTime ? new Date(candidate.scheduleDateTime).toLocaleString() : 'Not scheduled'}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <StatusBadge status={candidate.interviewStatus} />
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      {candidate.metrics ? `${candidate.metrics.overallScore}/100` : 'N/A'}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm">
                      <div className="flex gap-2">
                        <Link to={`/candidates/${candidate.id}`}>
                          <Button variant="ghost" size="sm">View</Button>
                        </Link>
                        {candidate.interviewStatus === 'Scheduled' && (
                          <Button variant="outline" size="sm">Reschedule</Button>
                        )}
                        {candidate.interviewStatus === 'Not Scheduled' && (
                          <Button size="sm" className="bg-primary hover:bg-primary/90">Schedule</Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
      
      <div className="mt-6 flex justify-between items-center">
        <div className="text-sm text-gray-500">
          Showing {interviewCandidates.length} of {mockCandidates.length} interviews
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" disabled>Previous</Button>
          <Button variant="outline" size="sm" disabled>Next</Button>
        </div>
      </div>
    </div>
  );
};

export default Interviews;
