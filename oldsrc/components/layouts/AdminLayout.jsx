
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { UserDropdown } from '@/components/common/UserDropdown';
import { Logo } from '@/components/common/Logo';
import { 
  Home, Users, Settings, Phone, AlertCircle 
} from 'lucide-react';

export function AdminLayout({ children }) {
  const { user, logout } = useAuth();
  const location = useLocation();

  const isActive = (path) => location.pathname === path;

  const navigation = [
    { name: 'Dashboard', href: '/admin/dashboard', icon: Home },
    { name: 'Users & Subscriptions', href: '/admin/users', icon: Users },
    { name: 'Account Management', href: '/admin/accounts', icon: Settings },
    { name: 'Twilio Numbers', href: '/admin/twilio', icon: Phone },
    { name: 'Issues', href: '/admin/issues', icon: AlertCircle },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm z-10 relative">
        <div className="container-page py-4">
          <div className="flex justify-between items-center">
            <Link to="/admin/dashboard" className="flex items-center">
              <Logo />
              <span className="ml-2 text-primary font-semibold">Admin</span>
            </Link>
            
            {user && (
              <UserDropdown userEmail={user.email} onLogout={logout} />
            )}
          </div>
        </div>
      </header>

      {/* Sidebar and content */}
      <div className="flex">
        {/* Sidebar */}
        <aside className="hidden md:flex flex-col w-64 bg-white border-r min-h-[calc(100vh-64px)]">
          <nav className="flex flex-col flex-1 pt-5">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`flex items-center px-4 py-2 mx-3 my-1 text-sm font-medium rounded-md ${
                  isActive(item.href)
                    ? 'bg-primary text-white'
                    : 'text-primary hover:bg-gray-100'
                }`}
              >
                <item.icon className="mr-3 h-5 w-5" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </nav>
        </aside>

        {/* Mobile navigation */}
        <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t z-10">
          <div className="grid grid-cols-5 gap-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`flex flex-col items-center justify-center py-2 ${
                  isActive(item.href)
                    ? 'text-primary'
                    : 'text-gray-500'
                }`}
              >
                <item.icon className="h-5 w-5" aria-hidden="true" />
                <span className="text-xs mt-1">{item.name}</span>
              </Link>
            ))}
          </div>
        </div>

        {/* Main content */}
        <main className="flex-1 min-w-0">
          <div className="container-page">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
