/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.js */ \"(rsc)/./src/app/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/projects/agent-alex/src/app/layout.js\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS5qcyZhcHBEaXI9JTJGVXNlcnMlMkZ2YXJ1bnNvbmklMkZEZXNrdG9wJTJGcHJvamVjdHMlMkZhZ2VudC1hbGV4JTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZVc2VycyUyRnZhcnVuc29uaSUyRkRlc2t0b3AlMkZwcm9qZWN0cyUyRmFnZW50LWFsZXgmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNCQUFzQixrSkFBbUc7QUFDekgsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQixnT0FBbUY7QUFDekcsb0JBQW9CLDhJQUFpRztBQUduSDtBQUdBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdmFydW5zb25pL0Rlc2t0b3AvcHJvamVjdHMvYWdlbnQtYWxleC9zcmMvYXBwL2xheW91dC5qc1wiKTtcbmNvbnN0IG1vZHVsZTEgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMyA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiKTtcbmNvbnN0IHBhZ2U0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdmFydW5zb25pL0Rlc2t0b3AvcHJvamVjdHMvYWdlbnQtYWxleC9zcmMvYXBwL3BhZ2UuanNcIik7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNzcidcbn07XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFtwYWdlNCwgXCIvVXNlcnMvdmFydW5zb25pL0Rlc2t0b3AvcHJvamVjdHMvYWdlbnQtYWxleC9zcmMvYXBwL3BhZ2UuanNcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbbW9kdWxlMCwgXCIvVXNlcnMvdmFydW5zb25pL0Rlc2t0b3AvcHJvamVjdHMvYWdlbnQtYWxleC9zcmMvYXBwL2xheW91dC5qc1wiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCIvVXNlcnMvdmFydW5zb25pL0Rlc2t0b3AvcHJvamVjdHMvYWdlbnQtYWxleC9zcmMvYXBwL3BhZ2UuanNcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJycsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.js */ \"(rsc)/./src/app/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdmFydW5zb25pJTJGRGVza3RvcCUyRnByb2plY3RzJTJGYWdlbnQtYWxleCUyRnNyYyUyRmFwcCUyRnBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhJQUFpRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3ZhcnVuc29uaS9EZXNrdG9wL3Byb2plY3RzL2FnZW50LWFsZXgvc3JjL2FwcC9wYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2b3629ad9c99\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvdmFydW5zb25pL0Rlc2t0b3AvcHJvamVjdHMvYWdlbnQtYWxleC9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmIzNjI5YWQ5Yzk5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: 'Agent Alex - AI Enterprise App',\n    description: 'Enterprise-level React app with AWS Bedrock integration'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/layout.js\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/layout.js\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBc0I7QUFFZixNQUFNQSxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUFFQyxRQUFRLEVBQUU7SUFDN0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUFNSDs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsiL1VzZXJzL3ZhcnVuc29uaS9EZXNrdG9wL3Byb2plY3RzL2FnZW50LWFsZXgvc3JjL2FwcC9sYXlvdXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnQWdlbnQgQWxleCAtIEFJIEVudGVycHJpc2UgQXBwJyxcbiAgZGVzY3JpcHRpb246ICdFbnRlcnByaXNlLWxldmVsIFJlYWN0IGFwcCB3aXRoIEFXUyBCZWRyb2NrIGludGVncmF0aW9uJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7IGNoaWxkcmVuIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.js */ \"(ssr)/./src/app/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdmFydW5zb25pJTJGRGVza3RvcCUyRnByb2plY3RzJTJGYWdlbnQtYWxleCUyRnNyYyUyRmFwcCUyRnBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhJQUFpRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3ZhcnVuc29uaS9EZXNrdG9wL3Byb2plY3RzL2FnZW50LWFsZXgvc3JjL2FwcC9wYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AIChat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AIChat */ \"(ssr)/./src/components/AIChat.js\");\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Dashboard */ \"(ssr)/./src/components/Dashboard.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Home() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                style: {\n                    textAlign: 'center',\n                    marginBottom: '40px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '3rem',\n                            marginBottom: '10px'\n                        },\n                        children: \"Agent Alex\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: 'rgba(255,255,255,0.8)',\n                            fontSize: '1.2rem'\n                        },\n                        children: \"Enterprise AI Application with AWS Bedrock Integration\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                style: {\n                    marginBottom: '30px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        gap: '10px',\n                        justifyContent: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: `button ${activeTab === 'dashboard' ? 'active' : ''}`,\n                            onClick: ()=>setActiveTab('dashboard'),\n                            style: {\n                                background: activeTab === 'dashboard' ? '#5a6fd8' : 'rgba(255,255,255,0.2)',\n                                color: 'white'\n                            },\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: `button ${activeTab === 'chat' ? 'active' : ''}`,\n                            onClick: ()=>setActiveTab('chat'),\n                            style: {\n                                background: activeTab === 'chat' ? '#5a6fd8' : 'rgba(255,255,255,0.2)',\n                                color: 'white'\n                            },\n                            children: \"AI Chat\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    activeTab === 'dashboard' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                        lineNumber: 47,\n                        columnNumber: 39\n                    }, this),\n                    activeTab === 'chat' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIChat__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                        lineNumber: 48,\n                        columnNumber: 34\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.js\n");

/***/ }),

/***/ "(ssr)/./src/components/AIChat.js":
/*!**********************************!*\
  !*** ./src/components/AIChat.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AIChat() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            text: \"Hello! I'm your AI assistant powered by AWS Bedrock. How can I help you today?\",\n            sender: 'ai',\n            timestamp: new Date().toLocaleTimeString()\n        }\n    ]);\n    const [inputText, setInputText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Placeholder function for sending messages to AI\n    const sendMessage = async ()=>{\n        if (!inputText.trim()) return;\n        const userMessage = {\n            id: Date.now(),\n            text: inputText,\n            sender: 'user',\n            timestamp: new Date().toLocaleTimeString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputText('');\n        setLoading(true);\n        try {\n            // TODO: Replace with actual AWS Bedrock API call\n            const response = await fetch('/api/ai/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: inputText,\n                    model: 'claude-3-sonnet'\n                })\n            });\n            let aiResponse;\n            if (response.ok) {\n                const data = await response.json();\n                aiResponse = data.response;\n            } else {\n                // Fallback response when API is not connected\n                aiResponse = `I received your message: \"${inputText}\". This is a placeholder response. Once AWS Bedrock is connected, I'll provide intelligent responses using Claude or other foundation models.`;\n            }\n            const aiMessage = {\n                id: Date.now() + 1,\n                text: aiResponse,\n                sender: 'ai',\n                timestamp: new Date().toLocaleTimeString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.log('API not connected yet, using placeholder response');\n            const aiMessage = {\n                id: Date.now() + 1,\n                text: `I received your message: \"${inputText}\". This is a placeholder response. Once AWS Bedrock is connected, I'll provide intelligent responses using Claude or other foundation models.`,\n                sender: 'ai',\n                timestamp: new Date().toLocaleTimeString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        }\n        setLoading(false);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        style: {\n            height: '600px',\n            display: 'flex',\n            flexDirection: 'column'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                style: {\n                    marginBottom: '20px',\n                    color: '#333'\n                },\n                children: \"AI Chat - AWS Bedrock Integration\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    border: '1px solid #e2e8f0',\n                    borderRadius: '8px',\n                    padding: '15px',\n                    marginBottom: '15px',\n                    backgroundColor: '#f8f9fa'\n                },\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '15px',\n                                display: 'flex',\n                                justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: '70%',\n                                    padding: '12px 16px',\n                                    borderRadius: '18px',\n                                    backgroundColor: message.sender === 'user' ? '#667eea' : '#ffffff',\n                                    color: message.sender === 'user' ? 'white' : '#333',\n                                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '5px'\n                                        },\n                                        children: message.text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '0.75rem',\n                                            opacity: 0.7,\n                                            textAlign: 'right'\n                                        },\n                                        children: message.timestamp\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        }, message.id, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'flex-start',\n                            marginBottom: '15px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '12px 16px',\n                                borderRadius: '18px',\n                                backgroundColor: '#ffffff',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    gap: '10px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: inputText,\n                        onChange: (e)=>setInputText(e.target.value),\n                        onKeyPress: handleKeyPress,\n                        placeholder: \"Type your message here... (Press Enter to send)\",\n                        style: {\n                            flex: 1,\n                            padding: '12px',\n                            border: '1px solid #e2e8f0',\n                            borderRadius: '8px',\n                            resize: 'none',\n                            minHeight: '50px',\n                            fontFamily: 'inherit'\n                        },\n                        disabled: loading\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: sendMessage,\n                        disabled: loading || !inputText.trim(),\n                        className: \"button\",\n                        style: {\n                            alignSelf: 'flex-end',\n                            minWidth: '80px'\n                        },\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                            lineNumber: 166,\n                            columnNumber: 22\n                        }, this) : 'Send'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: '10px',\n                    fontSize: '0.9rem',\n                    color: '#666'\n                },\n                children: \"\\uD83D\\uDCA1 This chat will be powered by AWS Bedrock (Claude, Llama, etc.) once backend is connected\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AIChat.js\n");

/***/ }),

/***/ "(ssr)/./src/components/Dashboard.js":
/*!*************************************!*\
  !*** ./src/components/Dashboard.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Dashboard() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalRequests: 0,\n        activeAgents: 0,\n        successRate: 0,\n        avgResponseTime: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Placeholder function for fetching dashboard data\n    const fetchDashboardData = async ()=>{\n        setLoading(true);\n        try {\n            // TODO: Replace with actual API call to AWS Lambda\n            const response = await fetch('/api/dashboard/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setStats(data);\n            } else {\n                // Fallback to mock data\n                setStats({\n                    totalRequests: 1247,\n                    activeAgents: 5,\n                    successRate: 98.5,\n                    avgResponseTime: 245\n                });\n            }\n        } catch (error) {\n            console.log('Using mock data - API not connected yet');\n            // Mock data for development\n            setStats({\n                totalRequests: 1247,\n                activeAgents: 5,\n                successRate: 98.5,\n                avgResponseTime: 245\n            });\n        }\n        setLoading(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            fetchDashboardData();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const StatCard = ({ title, value, unit, color })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card\",\n            style: {\n                textAlign: 'center'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    style: {\n                        color: '#666',\n                        marginBottom: '10px'\n                    },\n                    children: title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                    lineNumber: 51,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        fontSize: '2.5rem',\n                        fontWeight: 'bold',\n                        color: color || '#667eea'\n                    },\n                    children: [\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                            lineNumber: 53,\n                            columnNumber: 20\n                        }, this) : value,\n                        !loading && unit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: '1rem',\n                                color: '#999'\n                            },\n                            children: unit\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                            lineNumber: 54,\n                            columnNumber: 30\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                    lineNumber: 52,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n            lineNumber: 50,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                    gap: '20px',\n                    marginBottom: '30px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Total Requests\",\n                        value: stats.totalRequests.toLocaleString(),\n                        color: \"#667eea\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Active AI Agents\",\n                        value: stats.activeAgents,\n                        color: \"#48bb78\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Success Rate\",\n                        value: stats.successRate,\n                        unit: \"%\",\n                        color: \"#ed8936\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Avg Response Time\",\n                        value: stats.avgResponseTime,\n                        unit: \"ms\",\n                        color: \"#9f7aea\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            marginBottom: '20px',\n                            color: '#333'\n                        },\n                        children: \"AWS Services Status\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gap: '15px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServiceStatus, {\n                                name: \"AWS Bedrock\",\n                                status: \"connected\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServiceStatus, {\n                                name: \"Lambda Functions\",\n                                status: \"connected\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServiceStatus, {\n                                name: \"DynamoDB\",\n                                status: \"connected\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServiceStatus, {\n                                name: \"S3 Storage\",\n                                status: \"connected\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            marginBottom: '20px',\n                            color: '#333'\n                        },\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '15px',\n                            flexWrap: 'wrap'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"button\",\n                                onClick: ()=>alert('Deploy new agent - API integration needed'),\n                                children: \"Deploy New Agent\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"button\",\n                                onClick: ()=>alert('View logs - API integration needed'),\n                                children: \"View Logs\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"button\",\n                                onClick: fetchDashboardData,\n                                children: \"Refresh Data\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\nfunction ServiceStatus({ name, status }) {\n    const statusColor = status === 'connected' ? '#48bb78' : '#f56565';\n    const statusText = status === 'connected' ? 'Connected' : 'Disconnected';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            padding: '10px',\n            border: '1px solid #e2e8f0',\n            borderRadius: '8px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    fontWeight: '500'\n                },\n                children: name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: statusColor,\n                    fontWeight: 'bold',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '5px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '8px',\n                            height: '8px',\n                            borderRadius: '50%',\n                            backgroundColor: statusColor\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    statusText\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Dashboard.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();