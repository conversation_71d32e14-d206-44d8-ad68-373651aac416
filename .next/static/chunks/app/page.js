/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.js */ \"(app-pages-browser)/./src/app/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ2YXJ1bnNvbmklMkZEZXNrdG9wJTJGcHJvamVjdHMlMkZhZ2VudC1hbGV4JTJGc3JjJTJGYXBwJTJGcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFpRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3ZhcnVuc29uaS9EZXNrdG9wL3Byb2plY3RzL2FnZW50LWFsZXgvc3JjL2FwcC9wYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy92YXJ1bnNvbmkvRGVza3RvcC9wcm9qZWN0cy9hZ2VudC1hbGV4L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AIChat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AIChat */ \"(app-pages-browser)/./src/components/AIChat.js\");\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Dashboard */ \"(app-pages-browser)/./src/components/Dashboard.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                style: {\n                    textAlign: 'center',\n                    marginBottom: '40px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '3rem',\n                            marginBottom: '10px'\n                        },\n                        children: \"Agent Alex\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: 'rgba(255,255,255,0.8)',\n                            fontSize: '1.2rem'\n                        },\n                        children: \"Enterprise AI Application with AWS Bedrock Integration\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                style: {\n                    marginBottom: '30px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        gap: '10px',\n                        justifyContent: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"button \".concat(activeTab === 'dashboard' ? 'active' : ''),\n                            onClick: ()=>setActiveTab('dashboard'),\n                            style: {\n                                background: activeTab === 'dashboard' ? '#5a6fd8' : 'rgba(255,255,255,0.2)',\n                                color: 'white'\n                            },\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"button \".concat(activeTab === 'chat' ? 'active' : ''),\n                            onClick: ()=>setActiveTab('chat'),\n                            style: {\n                                background: activeTab === 'chat' ? '#5a6fd8' : 'rgba(255,255,255,0.2)',\n                                color: 'white'\n                            },\n                            children: \"AI Chat\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    activeTab === 'dashboard' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                        lineNumber: 47,\n                        columnNumber: 39\n                    }, this),\n                    activeTab === 'chat' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIChat__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                        lineNumber: 48,\n                        columnNumber: 34\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/app/page.js\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"EZIq+yjoOG1DNoxcFU03DF5qjSk=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AIChat.js":
/*!**********************************!*\
  !*** ./src/components/AIChat.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction AIChat() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            text: \"Hello! I'm your AI assistant powered by AWS Bedrock. How can I help you today?\",\n            sender: 'ai',\n            timestamp: new Date().toLocaleTimeString()\n        }\n    ]);\n    const [inputText, setInputText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Placeholder function for sending messages to AI\n    const sendMessage = async ()=>{\n        if (!inputText.trim()) return;\n        const userMessage = {\n            id: Date.now(),\n            text: inputText,\n            sender: 'user',\n            timestamp: new Date().toLocaleTimeString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputText('');\n        setLoading(true);\n        try {\n            // TODO: Replace with actual AWS Bedrock API call\n            const response = await fetch('/api/ai/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: inputText,\n                    model: 'claude-3-sonnet'\n                })\n            });\n            let aiResponse;\n            if (response.ok) {\n                const data = await response.json();\n                aiResponse = data.response;\n            } else {\n                // Fallback response when API is not connected\n                aiResponse = 'I received your message: \"'.concat(inputText, \"\\\". This is a placeholder response. Once AWS Bedrock is connected, I'll provide intelligent responses using Claude or other foundation models.\");\n            }\n            const aiMessage = {\n                id: Date.now() + 1,\n                text: aiResponse,\n                sender: 'ai',\n                timestamp: new Date().toLocaleTimeString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.log('API not connected yet, using placeholder response');\n            const aiMessage = {\n                id: Date.now() + 1,\n                text: 'I received your message: \"'.concat(inputText, \"\\\". This is a placeholder response. Once AWS Bedrock is connected, I'll provide intelligent responses using Claude or other foundation models.\"),\n                sender: 'ai',\n                timestamp: new Date().toLocaleTimeString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        }\n        setLoading(false);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        style: {\n            height: '600px',\n            display: 'flex',\n            flexDirection: 'column'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                style: {\n                    marginBottom: '20px',\n                    color: '#333'\n                },\n                children: \"AI Chat - AWS Bedrock Integration\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    border: '1px solid #e2e8f0',\n                    borderRadius: '8px',\n                    padding: '15px',\n                    marginBottom: '15px',\n                    backgroundColor: '#f8f9fa'\n                },\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '15px',\n                                display: 'flex',\n                                justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: '70%',\n                                    padding: '12px 16px',\n                                    borderRadius: '18px',\n                                    backgroundColor: message.sender === 'user' ? '#667eea' : '#ffffff',\n                                    color: message.sender === 'user' ? 'white' : '#333',\n                                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '5px'\n                                        },\n                                        children: message.text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '0.75rem',\n                                            opacity: 0.7,\n                                            textAlign: 'right'\n                                        },\n                                        children: message.timestamp\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        }, message.id, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'flex-start',\n                            marginBottom: '15px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '12px 16px',\n                                borderRadius: '18px',\n                                backgroundColor: '#ffffff',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    gap: '10px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: inputText,\n                        onChange: (e)=>setInputText(e.target.value),\n                        onKeyPress: handleKeyPress,\n                        placeholder: \"Type your message here... (Press Enter to send)\",\n                        style: {\n                            flex: 1,\n                            padding: '12px',\n                            border: '1px solid #e2e8f0',\n                            borderRadius: '8px',\n                            resize: 'none',\n                            minHeight: '50px',\n                            fontFamily: 'inherit'\n                        },\n                        disabled: loading\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: sendMessage,\n                        disabled: loading || !inputText.trim(),\n                        className: \"button\",\n                        style: {\n                            alignSelf: 'flex-end',\n                            minWidth: '80px'\n                        },\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                            lineNumber: 166,\n                            columnNumber: 22\n                        }, this) : 'Send'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: '10px',\n                    fontSize: '0.9rem',\n                    color: '#666'\n                },\n                children: \"\\uD83D\\uDCA1 This chat will be powered by AWS Bedrock (Claude, Llama, etc.) once backend is connected\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/AIChat.js\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n_s(AIChat, \"tqp5Fr3KwQdggr6YCzsrTiCYeAE=\");\n_c = AIChat;\nvar _c;\n$RefreshReg$(_c, \"AIChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AIChat.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Dashboard.js":
/*!*************************************!*\
  !*** ./src/components/Dashboard.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Dashboard() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalRequests: 0,\n        activeAgents: 0,\n        successRate: 0,\n        avgResponseTime: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Placeholder function for fetching dashboard data\n    const fetchDashboardData = async ()=>{\n        setLoading(true);\n        try {\n            // TODO: Replace with actual API call to AWS Lambda\n            const response = await fetch('/api/dashboard/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setStats(data);\n            } else {\n                // Fallback to mock data\n                setStats({\n                    totalRequests: 1247,\n                    activeAgents: 5,\n                    successRate: 98.5,\n                    avgResponseTime: 245\n                });\n            }\n        } catch (error) {\n            console.log('Using mock data - API not connected yet');\n            // Mock data for development\n            setStats({\n                totalRequests: 1247,\n                activeAgents: 5,\n                successRate: 98.5,\n                avgResponseTime: 245\n            });\n        }\n        setLoading(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            fetchDashboardData();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const StatCard = (param)=>{\n        let { title, value, unit, color } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card\",\n            style: {\n                textAlign: 'center'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    style: {\n                        color: '#666',\n                        marginBottom: '10px'\n                    },\n                    children: title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                    lineNumber: 51,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        fontSize: '2.5rem',\n                        fontWeight: 'bold',\n                        color: color || '#667eea'\n                    },\n                    children: [\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                            lineNumber: 53,\n                            columnNumber: 20\n                        }, this) : value,\n                        !loading && unit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: '1rem',\n                                color: '#999'\n                            },\n                            children: unit\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                            lineNumber: 54,\n                            columnNumber: 30\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                    lineNumber: 52,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n            lineNumber: 50,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                    gap: '20px',\n                    marginBottom: '30px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Total Requests\",\n                        value: stats.totalRequests.toLocaleString(),\n                        color: \"#667eea\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Active AI Agents\",\n                        value: stats.activeAgents,\n                        color: \"#48bb78\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Success Rate\",\n                        value: stats.successRate,\n                        unit: \"%\",\n                        color: \"#ed8936\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Avg Response Time\",\n                        value: stats.avgResponseTime,\n                        unit: \"ms\",\n                        color: \"#9f7aea\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            marginBottom: '20px',\n                            color: '#333'\n                        },\n                        children: \"AWS Services Status\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gap: '15px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServiceStatus, {\n                                name: \"AWS Bedrock\",\n                                status: \"connected\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServiceStatus, {\n                                name: \"Lambda Functions\",\n                                status: \"connected\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServiceStatus, {\n                                name: \"DynamoDB\",\n                                status: \"connected\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServiceStatus, {\n                                name: \"S3 Storage\",\n                                status: \"connected\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            marginBottom: '20px',\n                            color: '#333'\n                        },\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '15px',\n                            flexWrap: 'wrap'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"button\",\n                                onClick: ()=>alert('Deploy new agent - API integration needed'),\n                                children: \"Deploy New Agent\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"button\",\n                                onClick: ()=>alert('View logs - API integration needed'),\n                                children: \"View Logs\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"button\",\n                                onClick: fetchDashboardData,\n                                children: \"Refresh Data\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"y57IfwOnx/5FQA3AuxX5w6mvvI4=\");\n_c = Dashboard;\nfunction ServiceStatus(param) {\n    let { name, status } = param;\n    const statusColor = status === 'connected' ? '#48bb78' : '#f56565';\n    const statusText = status === 'connected' ? 'Connected' : 'Disconnected';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            padding: '10px',\n            border: '1px solid #e2e8f0',\n            borderRadius: '8px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    fontWeight: '500'\n                },\n                children: name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: statusColor,\n                    fontWeight: 'bold',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '5px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '8px',\n                            height: '8px',\n                            borderRadius: '50%',\n                            backgroundColor: statusColor\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    statusText\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/agent-alex/src/components/Dashboard.js\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ServiceStatus;\nvar _c, _c1;\n$RefreshReg$(_c, \"Dashboard\");\n$RefreshReg$(_c1, \"ServiceStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvarunsoni%2FDesktop%2Fprojects%2Fagent-alex%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);