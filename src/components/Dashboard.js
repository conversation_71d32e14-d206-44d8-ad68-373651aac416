'use client'

import { useState, useEffect } from 'react'
import apiService from '../services/api'

export default function Dashboard() {
  const [stats, setStats] = useState({
    totalRequests: 0,
    activeAgents: 0,
    successRate: 0,
    avgResponseTime: 0
  })
  const [loading, setLoading] = useState(false)

  // Function for fetching dashboard data using API service
  const fetchDashboardData = async () => {
    setLoading(true)
    try {
      const data = await apiService.dashboard.getStats()
      setStats(data)
    } catch (error) {
      console.error('Dashboard data fetch error:', error)
      // Fallback to mock data
      setStats({
        totalRequests: 1247,
        activeAgents: 5,
        successRate: 98.5,
        avgResponseTime: 245
      })
    }
    setLoading(false)
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const StatCard = ({ title, value, unit, color }) => (
    <div className="card" style={{ textAlign: 'center' }}>
      <h3 style={{ color: '#666', marginBottom: '10px' }}>{title}</h3>
      <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: color || '#667eea' }}>
        {loading ? <div className="loading"></div> : value}
        {!loading && unit && <span style={{ fontSize: '1rem', color: '#999' }}>{unit}</span>}
      </div>
    </div>
  )

  return (
    <div>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px', marginBottom: '30px' }}>
        <StatCard 
          title="Total Requests" 
          value={stats.totalRequests.toLocaleString()} 
          color="#667eea"
        />
        <StatCard 
          title="Active AI Agents" 
          value={stats.activeAgents} 
          color="#48bb78"
        />
        <StatCard 
          title="Success Rate" 
          value={stats.successRate} 
          unit="%" 
          color="#ed8936"
        />
        <StatCard 
          title="Avg Response Time" 
          value={stats.avgResponseTime} 
          unit="ms" 
          color="#9f7aea"
        />
      </div>

      <div className="card">
        <h2 style={{ marginBottom: '20px', color: '#333' }}>AWS Services Status</h2>
        <div style={{ display: 'grid', gap: '15px' }}>
          <ServiceStatus name="AWS Bedrock" status="connected" />
          <ServiceStatus name="Lambda Functions" status="connected" />
          <ServiceStatus name="DynamoDB" status="connected" />
          <ServiceStatus name="S3 Storage" status="connected" />
        </div>
      </div>

      <div className="card">
        <h2 style={{ marginBottom: '20px', color: '#333' }}>Quick Actions</h2>
        <div style={{ display: 'flex', gap: '15px', flexWrap: 'wrap' }}>
          <button className="button" onClick={() => alert('Deploy new agent - API integration needed')}>
            Deploy New Agent
          </button>
          <button className="button" onClick={() => alert('View logs - API integration needed')}>
            View Logs
          </button>
          <button className="button" onClick={fetchDashboardData}>
            Refresh Data
          </button>
        </div>
      </div>
    </div>
  )
}

function ServiceStatus({ name, status }) {
  const statusColor = status === 'connected' ? '#48bb78' : '#f56565'
  const statusText = status === 'connected' ? 'Connected' : 'Disconnected'
  
  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      padding: '10px',
      border: '1px solid #e2e8f0',
      borderRadius: '8px'
    }}>
      <span style={{ fontWeight: '500' }}>{name}</span>
      <span style={{ 
        color: statusColor, 
        fontWeight: 'bold',
        display: 'flex',
        alignItems: 'center',
        gap: '5px'
      }}>
        <div style={{
          width: '8px',
          height: '8px',
          borderRadius: '50%',
          backgroundColor: statusColor
        }}></div>
        {statusText}
      </span>
    </div>
  )
}
