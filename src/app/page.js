'use client'

import { useState } from 'react'
import AIChat from '../components/AIChat'
import Dashboard from '../components/Dashboard'

export default function Home() {
  const [activeTab, setActiveTab] = useState('dashboard')

  return (
    <div className="container">
      <header style={{ textAlign: 'center', marginBottom: '40px' }}>
        <h1 style={{ color: 'white', fontSize: '3rem', marginBottom: '10px' }}>
          Agent Alex
        </h1>
        <p style={{ color: 'rgba(255,255,255,0.8)', fontSize: '1.2rem' }}>
          Enterprise AI Application with AWS Bedrock Integration
        </p>
      </header>

      <nav style={{ marginBottom: '30px' }}>
        <div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
          <button 
            className={`button ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => setActiveTab('dashboard')}
            style={{ 
              background: activeTab === 'dashboard' ? '#5a6fd8' : 'rgba(255,255,255,0.2)',
              color: 'white'
            }}
          >
            Dashboard
          </button>
          <button 
            className={`button ${activeTab === 'chat' ? 'active' : ''}`}
            onClick={() => setActiveTab('chat')}
            style={{ 
              background: activeTab === 'chat' ? '#5a6fd8' : 'rgba(255,255,255,0.2)',
              color: 'white'
            }}
          >
            AI Chat
          </button>
        </div>
      </nav>

      <main>
        {activeTab === 'dashboard' && <Dashboard />}
        {activeTab === 'chat' && <AIChat />}
      </main>
    </div>
  )
}
