# Agent Alex - Enterprise AI Frontend

A modern React/Next.js frontend application built for enterprise-level AI integration with AWS Bedrock.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- npm or yarn package manager

### Installation & Running

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```

3. **Open in browser:**
   ```
   http://localhost:3000
   ```

## 📁 Project Structure

```
agent-alex/
├── frontend/                 # React/Next.js frontend
│   ├── src/
│   │   ├── app/             # Next.js App Router
│   │   │   ├── layout.js    # Root layout
│   │   │   ├── page.js      # Home page
│   │   │   ├── globals.css  # Global styles
│   │   │   └── api/         # API routes (for future backend)
│   │   ├── components/      # React components
│   │   │   ├── Dashboard.js # Main dashboard
│   │   │   └── AIChat.js    # AI chat interface
│   │   └── services/        # API service layer
│   │       └── api.js       # Centralized API calls
│   ├── package.json         # Frontend dependencies
│   └── next.config.js       # Next.js configuration
├── package.json             # Root package.json (runs frontend)
└── README.md               # This file
```

## 🎯 Current Features

### ✅ Dashboard
- Real-time statistics display
- AWS services status monitoring
- Quick action buttons
- Responsive design

### ✅ AI Chat Interface
- Interactive chat UI
- Message history
- Typing indicators
- Ready for AWS Bedrock integration

### ✅ API Service Layer
- Centralized API communication
- Error handling
- Automatic fallback support
- Ready for backend integration

## 🛠 Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Installation
npm install          # Install frontend dependencies
```

## 🔧 Configuration

### Environment Variables
Copy `frontend/.env.example` to `frontend/.env.local` and configure:

```env
# Next.js Public Variables
NEXT_PUBLIC_APP_NAME=Agent Alex
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000/api

# AWS Configuration (for future backend)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
```

## 🎨 UI Components

### Current Components:
- **Dashboard**: Stats cards, service status, analytics
- **AIChat**: Interactive chat with AI models
- **Navigation**: Tab-based interface

### Styling:
- Modern CSS with gradients
- Responsive design
- Card-based layout
- Loading states and animations

## 🔮 Ready for Backend Integration

The frontend is structured to easily integrate with:
- **FastAPI backend** (Python)
- **AWS Bedrock** for AI models
- **AWS Lambda** functions
- **DynamoDB** for data storage
- **S3** for file uploads

## 📱 Responsive Design

- ✅ Desktop optimized
- ✅ Tablet friendly
- ✅ Mobile responsive
- ✅ Modern browser support

## 🚀 Next Steps

Ready to add more pages and features! The architecture supports:
- User authentication
- Settings and preferences
- Analytics dashboards
- File uploads
- Real-time notifications
- And much more...

## 🤝 Development

The codebase is organized for easy scaling:
- Component-based architecture
- Service layer for API calls
- Environment-based configuration
- Modern React patterns (hooks, functional components)

---

**Built with:** React 18, Next.js 15, Modern CSS, AWS SDK Ready
