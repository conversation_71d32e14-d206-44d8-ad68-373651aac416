{"version": "2.0", "metadata": {"apiVersion": "2017-07-24", "endpointPrefix": "api.sagemaker", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceAbbreviation": "SageMaker", "serviceFullName": "Amazon SageMaker Service", "serviceId": "SageMaker", "signatureVersion": "v4", "signingName": "sagemaker", "targetPrefix": "SageMaker", "uid": "sagemaker-2017-07-24", "auth": ["aws.auth#sigv4"]}, "operations": {"AddAssociation": {"input": {"type": "structure", "required": ["SourceArn", "DestinationArn"], "members": {"SourceArn": {}, "DestinationArn": {}, "AssociationType": {}}}, "output": {"type": "structure", "members": {"SourceArn": {}, "DestinationArn": {}}}}, "AddTags": {"input": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "S7"}}}}, "AssociateTrialComponent": {"input": {"type": "structure", "required": ["TrialComponentName", "TrialName"], "members": {"TrialComponentName": {}, "TrialName": {}}}, "output": {"type": "structure", "members": {"TrialComponentArn": {}, "TrialArn": {}}}}, "BatchDescribeModelPackage": {"input": {"type": "structure", "required": ["ModelPackageArnList"], "members": {"ModelPackageArnList": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"ModelPackageSummaries": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["ModelPackageGroupName", "ModelPackageArn", "CreationTime", "InferenceSpecification", "ModelPackageStatus"], "members": {"ModelPackageGroupName": {}, "ModelPackageVersion": {"type": "integer"}, "ModelPackageArn": {}, "ModelPackageDescription": {}, "CreationTime": {"type": "timestamp"}, "InferenceSpecification": {"shape": "<PERSON>"}, "ModelPackageStatus": {}, "ModelApprovalStatus": {}}}}, "BatchDescribeModelPackageErrorMap": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["ErrorCode", "ErrorResponse"], "members": {"ErrorCode": {}, "ErrorResponse": {}}}}}}}, "CreateAction": {"input": {"type": "structure", "required": ["ActionName", "Source", "ActionType"], "members": {"ActionName": {}, "Source": {"shape": "S1w"}, "ActionType": {}, "Description": {}, "Status": {}, "Properties": {"shape": "S21"}, "MetadataProperties": {"shape": "S23"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "members": {"ActionArn": {}}}}, "CreateAlgorithm": {"input": {"type": "structure", "required": ["AlgorithmName", "TrainingSpecification"], "members": {"AlgorithmName": {}, "AlgorithmDescription": {}, "TrainingSpecification": {"shape": "S28"}, "InferenceSpecification": {"shape": "<PERSON>"}, "ValidationSpecification": {"shape": "S30"}, "CertifyForMarketplace": {"type": "boolean"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["AlgorithmArn"], "members": {"AlgorithmArn": {}}}}, "CreateApp": {"input": {"type": "structure", "required": ["DomainId", "AppType", "AppName"], "members": {"DomainId": {}, "UserProfileName": {}, "SpaceName": {}, "AppType": {}, "AppName": {}, "Tags": {"shape": "S7"}, "ResourceSpec": {"shape": "S4r"}}}, "output": {"type": "structure", "members": {"AppArn": {}}}}, "CreateAppImageConfig": {"input": {"type": "structure", "required": ["AppImageConfigName"], "members": {"AppImageConfigName": {}, "Tags": {"shape": "S7"}, "KernelGatewayImageConfig": {"shape": "S51"}, "JupyterLabAppImageConfig": {"shape": "S5a"}, "CodeEditorAppImageConfig": {"shape": "S5h"}}}, "output": {"type": "structure", "members": {"AppImageConfigArn": {}}}}, "CreateArtifact": {"input": {"type": "structure", "required": ["Source", "ArtifactType"], "members": {"ArtifactName": {}, "Source": {"shape": "S5l"}, "ArtifactType": {}, "Properties": {"shape": "S5p"}, "MetadataProperties": {"shape": "S23"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "members": {"ArtifactArn": {}}}}, "CreateAutoMLJob": {"input": {"type": "structure", "required": ["AutoMLJobName", "InputDataConfig", "OutputDataConfig", "RoleArn"], "members": {"AutoMLJobName": {}, "InputDataConfig": {"shape": "S5v"}, "OutputDataConfig": {"shape": "S63"}, "ProblemType": {}, "AutoMLJobObjective": {"shape": "S65"}, "AutoMLJobConfig": {"shape": "S67"}, "RoleArn": {}, "GenerateCandidateDefinitionsOnly": {"type": "boolean"}, "Tags": {"shape": "S7"}, "ModelDeployConfig": {"shape": "S6r"}}}, "output": {"type": "structure", "required": ["AutoMLJobArn"], "members": {"AutoMLJobArn": {}}}}, "CreateAutoMLJobV2": {"input": {"type": "structure", "required": ["AutoMLJobName", "AutoMLJobInputDataConfig", "OutputDataConfig", "AutoMLProblemTypeConfig", "RoleArn"], "members": {"AutoMLJobName": {}, "AutoMLJobInputDataConfig": {"shape": "S6x"}, "OutputDataConfig": {"shape": "S63"}, "AutoMLProblemTypeConfig": {"shape": "S6z"}, "RoleArn": {}, "Tags": {"shape": "S7"}, "SecurityConfig": {"shape": "S6c"}, "AutoMLJobObjective": {"shape": "S65"}, "ModelDeployConfig": {"shape": "S6r"}, "DataSplitConfig": {"shape": "S6n"}, "AutoMLComputeConfig": {"shape": "S7w"}}}, "output": {"type": "structure", "required": ["AutoMLJobArn"], "members": {"AutoMLJobArn": {}}}}, "CreateCluster": {"input": {"type": "structure", "required": ["ClusterName", "InstanceGroups"], "members": {"ClusterName": {}, "InstanceGroups": {"shape": "S81"}, "VpcConfig": {"shape": "S6d"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["ClusterArn"], "members": {"ClusterArn": {}}}}, "CreateCodeRepository": {"input": {"type": "structure", "required": ["CodeRepositoryName", "GitConfig"], "members": {"CodeRepositoryName": {}, "GitConfig": {"shape": "S8h"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["CodeRepositoryArn"], "members": {"CodeRepositoryArn": {}}}}, "CreateCompilationJob": {"input": {"type": "structure", "required": ["CompilationJobName", "RoleArn", "OutputConfig", "StoppingCondition"], "members": {"CompilationJobName": {}, "RoleArn": {}, "ModelPackageVersionArn": {}, "InputConfig": {"shape": "S8o"}, "OutputConfig": {"shape": "S8r"}, "VpcConfig": {"shape": "S8y"}, "StoppingCondition": {"shape": "S3y"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["CompilationJobArn"], "members": {"CompilationJobArn": {}}}}, "CreateContext": {"input": {"type": "structure", "required": ["ContextName", "Source", "ContextType"], "members": {"ContextName": {}, "Source": {"shape": "S97"}, "ContextType": {}, "Description": {}, "Properties": {"shape": "S21"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "members": {"ContextArn": {}}}}, "CreateDataQualityJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName", "DataQualityAppSpecification", "DataQualityJobInput", "DataQualityJobOutputConfig", "JobResources", "RoleArn"], "members": {"JobDefinitionName": {}, "DataQualityBaselineConfig": {"shape": "S9c"}, "DataQualityAppSpecification": {"shape": "S9g"}, "DataQualityJobInput": {"shape": "S9p"}, "DataQualityJobOutputConfig": {"shape": "Sa3"}, "JobResources": {"shape": "Sa9"}, "NetworkConfig": {"shape": "Sae"}, "RoleArn": {}, "StoppingCondition": {"shape": "<PERSON><PERSON>"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["JobDefinitionArn"], "members": {"JobDefinitionArn": {}}}}, "CreateDeviceFleet": {"input": {"type": "structure", "required": ["DeviceFleetName", "OutputConfig"], "members": {"DeviceFleetName": {}, "RoleArn": {}, "Description": {}, "OutputConfig": {"shape": "Sal"}, "Tags": {"shape": "S7"}, "EnableIotRoleAlias": {"type": "boolean"}}}}, "CreateDomain": {"input": {"type": "structure", "required": ["DomainName", "AuthMode", "DefaultUserSettings", "SubnetIds", "VpcId"], "members": {"DomainName": {}, "AuthMode": {}, "DefaultUserSettings": {"shape": "Sar"}, "DomainSettings": {"shape": "Scc"}, "SubnetIds": {"shape": "S6g"}, "VpcId": {}, "Tags": {"shape": "S7"}, "AppNetworkAccessType": {}, "HomeEfsFileSystemKmsKeyId": {"deprecated": true, "deprecatedMessage": "This property is deprecated, use KmsKeyId instead."}, "KmsKeyId": {}, "AppSecurityGroupManagement": {}, "DefaultSpaceSettings": {"shape": "Sco"}}}, "output": {"type": "structure", "members": {"DomainArn": {}, "Url": {}}}}, "CreateEdgeDeploymentPlan": {"input": {"type": "structure", "required": ["EdgeDeploymentPlanName", "ModelConfigs", "DeviceFleetName"], "members": {"EdgeDeploymentPlanName": {}, "ModelConfigs": {"shape": "Sct"}, "DeviceFleetName": {}, "Stages": {"shape": "Scv"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["EdgeDeploymentPlanArn"], "members": {"EdgeDeploymentPlanArn": {}}}}, "CreateEdgeDeploymentStage": {"input": {"type": "structure", "required": ["EdgeDeploymentPlanName", "Stages"], "members": {"EdgeDeploymentPlanName": {}, "Stages": {"shape": "Scv"}}}}, "CreateEdgePackagingJob": {"input": {"type": "structure", "required": ["EdgePackagingJobName", "CompilationJobName", "ModelName", "ModelVersion", "RoleArn", "OutputConfig"], "members": {"EdgePackagingJobName": {}, "CompilationJobName": {}, "ModelName": {}, "ModelVersion": {}, "RoleArn": {}, "OutputConfig": {"shape": "Sal"}, "ResourceKey": {}, "Tags": {"shape": "S7"}}}}, "CreateEndpoint": {"input": {"type": "structure", "required": ["EndpointName", "EndpointConfigName"], "members": {"EndpointName": {}, "EndpointConfigName": {}, "DeploymentConfig": {"shape": "Sdb"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["EndpointArn"], "members": {"EndpointArn": {}}}}, "CreateEndpointConfig": {"input": {"type": "structure", "required": ["EndpointConfigName", "ProductionVariants"], "members": {"EndpointConfigName": {}, "ProductionVariants": {"shape": "Sdt"}, "DataCaptureConfig": {"shape": "Seg"}, "Tags": {"shape": "S7"}, "KmsKeyId": {}, "AsyncInferenceConfig": {"shape": "Ser"}, "ExplainerConfig": {"shape": "Sez"}, "ShadowProductionVariants": {"shape": "Sdt"}, "ExecutionRoleArn": {}, "VpcConfig": {"shape": "S6d"}, "EnableNetworkIsolation": {"type": "boolean"}}}, "output": {"type": "structure", "required": ["EndpointConfigArn"], "members": {"EndpointConfigArn": {}}}}, "CreateExperiment": {"input": {"type": "structure", "required": ["ExperimentName"], "members": {"ExperimentName": {}, "DisplayName": {}, "Description": {}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "members": {"ExperimentArn": {}}}}, "CreateFeatureGroup": {"input": {"type": "structure", "required": ["FeatureGroupName", "RecordIdentifierFeatureName", "EventTimeFeatureName", "FeatureDefinitions"], "members": {"FeatureGroupName": {}, "RecordIdentifierFeatureName": {}, "EventTimeFeatureName": {}, "FeatureDefinitions": {"shape": "Sfy"}, "OnlineStoreConfig": {"shape": "Sg5"}, "OfflineStoreConfig": {"shape": "Sgb"}, "ThroughputConfig": {"type": "structure", "required": ["ThroughputMode"], "members": {"ThroughputMode": {}, "ProvisionedReadCapacityUnits": {"type": "integer"}, "ProvisionedWriteCapacityUnits": {"type": "integer"}}}, "RoleArn": {}, "Description": {}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["FeatureGroupArn"], "members": {"FeatureGroupArn": {}}}}, "CreateFlowDefinition": {"input": {"type": "structure", "required": ["FlowDefinitionName", "OutputConfig", "RoleArn"], "members": {"FlowDefinitionName": {}, "HumanLoopRequestSource": {"shape": "Sgq"}, "HumanLoopActivationConfig": {"shape": "Sgs"}, "HumanLoopConfig": {"shape": "Sgv"}, "OutputConfig": {"shape": "<PERSON><PERSON>"}, "RoleArn": {}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["FlowDefinitionArn"], "members": {"FlowDefinitionArn": {}}}}, "CreateHub": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "HubDescription"], "members": {"HubName": {}, "HubDescription": {}, "HubDisplayName": {}, "HubSearchKeywords": {"shape": "Shh"}, "S3StorageConfig": {"shape": "Shj"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"HubArn": {}}}}, "CreateHubContentReference": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "SageMakerPublicHubContentArn"], "members": {"HubName": {}, "SageMakerPublicHubContentArn": {}, "HubContentName": {}, "MinVersion": {}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "HubContentArn"], "members": {"HubArn": {}, "HubContentArn": {}}}}, "CreateHumanTaskUi": {"input": {"type": "structure", "required": ["HumanTaskUiName", "UiTemplate"], "members": {"HumanTaskUiName": {}, "UiTemplate": {"shape": "Shv"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["HumanTaskUiArn"], "members": {"HumanTaskUiArn": {}}}}, "CreateHyperParameterTuningJob": {"input": {"type": "structure", "required": ["HyperParameterTuningJobName", "HyperParameterTuningJobConfig"], "members": {"HyperParameterTuningJobName": {}, "HyperParameterTuningJobConfig": {"shape": "Si0"}, "TrainingJobDefinition": {"shape": "Sit"}, "TrainingJobDefinitions": {"shape": "Sj9"}, "WarmStartConfig": {"shape": "<PERSON><PERSON>"}, "Tags": {"shape": "S7"}, "Autotune": {"shape": "<PERSON><PERSON>"}}}, "output": {"type": "structure", "required": ["HyperParameterTuningJobArn"], "members": {"HyperParameterTuningJobArn": {}}}}, "CreateImage": {"input": {"type": "structure", "required": ["ImageName", "RoleArn"], "members": {"Description": {}, "DisplayName": {}, "ImageName": {}, "RoleArn": {}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "members": {"ImageArn": {}}}}, "CreateImageVersion": {"input": {"type": "structure", "required": ["BaseImage", "ClientToken", "ImageName"], "members": {"BaseImage": {}, "ClientToken": {"idempotencyToken": true}, "ImageName": {}, "Aliases": {"shape": "Sjp"}, "VendorGuidance": {}, "JobType": {}, "MLFramework": {}, "ProgrammingLang": {}, "Processor": {}, "Horovod": {"type": "boolean"}, "ReleaseNotes": {}}}, "output": {"type": "structure", "members": {"ImageVersionArn": {}}}}, "CreateInferenceComponent": {"input": {"type": "structure", "required": ["InferenceComponentName", "EndpointName", "VariantName", "Specification", "RuntimeConfig"], "members": {"InferenceComponentName": {}, "EndpointName": {}, "VariantName": {}, "Specification": {"shape": "Sk1"}, "RuntimeConfig": {"shape": "Sk8"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["InferenceComponentArn"], "members": {"InferenceComponentArn": {}}}}, "CreateInferenceExperiment": {"input": {"type": "structure", "required": ["Name", "Type", "RoleArn", "EndpointName", "ModelVariants", "ShadowModeConfig"], "members": {"Name": {}, "Type": {}, "Schedule": {"shape": "Skf"}, "Description": {}, "RoleArn": {}, "EndpointName": {}, "ModelVariants": {"shape": "Ski"}, "DataStorageConfig": {"shape": "Skq"}, "ShadowModeConfig": {"shape": "Skr"}, "KmsKey": {}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["InferenceExperimentArn"], "members": {"InferenceExperimentArn": {}}}}, "CreateInferenceRecommendationsJob": {"input": {"type": "structure", "required": ["JobName", "JobType", "RoleArn", "InputConfig"], "members": {"JobName": {}, "JobType": {}, "RoleArn": {}, "InputConfig": {"shape": "Skz"}, "JobDescription": {}, "StoppingConditions": {"shape": "Sm5"}, "OutputConfig": {"type": "structure", "members": {"KmsKeyId": {}, "CompiledOutputConfig": {"type": "structure", "members": {"S3OutputUri": {}}}}}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["JobArn"], "members": {"JobArn": {}}}}, "CreateLabelingJob": {"input": {"type": "structure", "required": ["LabelingJobName", "LabelAttributeName", "InputConfig", "OutputConfig", "RoleArn", "HumanTaskConfig"], "members": {"LabelingJobName": {}, "LabelAttributeName": {}, "InputConfig": {"shape": "Smh"}, "OutputConfig": {"shape": "Smo"}, "RoleArn": {}, "LabelCategoryConfigS3Uri": {}, "StoppingConditions": {"shape": "Smp"}, "LabelingJobAlgorithmsConfig": {"shape": "Sms"}, "HumanTaskConfig": {"shape": "Smw"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["LabelingJobArn"], "members": {"LabelingJobArn": {}}}}, "CreateMlflowTrackingServer": {"input": {"type": "structure", "required": ["TrackingServerName", "ArtifactStoreUri", "RoleArn"], "members": {"TrackingServerName": {}, "ArtifactStoreUri": {}, "TrackingServerSize": {}, "MlflowVersion": {}, "RoleArn": {}, "AutomaticModelRegistration": {"type": "boolean"}, "WeeklyMaintenanceWindowStart": {}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "members": {"TrackingServerArn": {}}}}, "CreateModel": {"input": {"type": "structure", "required": ["ModelName"], "members": {"ModelName": {}, "PrimaryContainer": {"shape": "Sni"}, "Containers": {"shape": "<PERSON><PERSON>"}, "InferenceExecutionConfig": {"shape": "Snv"}, "ExecutionRoleArn": {}, "Tags": {"shape": "S7"}, "VpcConfig": {"shape": "S6d"}, "EnableNetworkIsolation": {"type": "boolean"}}}, "output": {"type": "structure", "required": ["ModelArn"], "members": {"ModelArn": {}}}}, "CreateModelBiasJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName", "ModelBiasAppSpecification", "ModelBiasJobInput", "ModelBiasJobOutputConfig", "JobResources", "RoleArn"], "members": {"JobDefinitionName": {}, "ModelBiasBaselineConfig": {"shape": "Snz"}, "ModelBiasAppSpecification": {"shape": "So0"}, "ModelBiasJobInput": {"shape": "So1"}, "ModelBiasJobOutputConfig": {"shape": "Sa3"}, "JobResources": {"shape": "Sa9"}, "NetworkConfig": {"shape": "Sae"}, "RoleArn": {}, "StoppingCondition": {"shape": "<PERSON><PERSON>"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["JobDefinitionArn"], "members": {"JobDefinitionArn": {}}}}, "CreateModelCard": {"input": {"type": "structure", "required": ["ModelCardName", "Content", "ModelCardStatus"], "members": {"ModelCardName": {}, "SecurityConfig": {"shape": "So5"}, "Content": {"shape": "So6"}, "ModelCardStatus": {}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["ModelCardArn"], "members": {"ModelCardArn": {}}}}, "CreateModelCardExportJob": {"input": {"type": "structure", "required": ["ModelCardName", "ModelCardExportJobName", "OutputConfig"], "members": {"ModelCardName": {}, "ModelCardVersion": {"type": "integer"}, "ModelCardExportJobName": {}, "OutputConfig": {"shape": "Soc"}}}, "output": {"type": "structure", "required": ["ModelCardExportJobArn"], "members": {"ModelCardExportJobArn": {}}}}, "CreateModelExplainabilityJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName", "ModelExplainabilityAppSpecification", "ModelExplainabilityJobInput", "ModelExplainabilityJobOutputConfig", "JobResources", "RoleArn"], "members": {"JobDefinitionName": {}, "ModelExplainabilityBaselineConfig": {"shape": "Sog"}, "ModelExplainabilityAppSpecification": {"shape": "Soh"}, "ModelExplainabilityJobInput": {"shape": "Soi"}, "ModelExplainabilityJobOutputConfig": {"shape": "Sa3"}, "JobResources": {"shape": "Sa9"}, "NetworkConfig": {"shape": "Sae"}, "RoleArn": {}, "StoppingCondition": {"shape": "<PERSON><PERSON>"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["JobDefinitionArn"], "members": {"JobDefinitionArn": {}}}}, "CreateModelPackage": {"input": {"type": "structure", "members": {"ModelPackageName": {}, "ModelPackageGroupName": {}, "ModelPackageDescription": {}, "InferenceSpecification": {"shape": "<PERSON>"}, "ValidationSpecification": {"shape": "Sol"}, "SourceAlgorithmSpecification": {"shape": "<PERSON>"}, "CertifyForMarketplace": {"type": "boolean"}, "Tags": {"shape": "S7"}, "ModelApprovalStatus": {}, "MetadataProperties": {"shape": "S23"}, "ModelMetrics": {"shape": "Sor"}, "ClientToken": {"idempotencyToken": true}, "Domain": {}, "Task": {}, "SamplePayloadUrl": {}, "CustomerMetadataProperties": {"shape": "Soy"}, "DriftCheckBaselines": {"shape": "Sp1"}, "AdditionalInferenceSpecifications": {"shape": "Sp7"}, "SkipModelValidation": {}, "SourceUri": {}, "SecurityConfig": {"shape": "Spb"}, "ModelCard": {"shape": "Spc"}}}, "output": {"type": "structure", "required": ["ModelPackageArn"], "members": {"ModelPackageArn": {}}}}, "CreateModelPackageGroup": {"input": {"type": "structure", "required": ["ModelPackageGroupName"], "members": {"ModelPackageGroupName": {}, "ModelPackageGroupDescription": {}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["ModelPackageGroupArn"], "members": {"ModelPackageGroupArn": {}}}}, "CreateModelQualityJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName", "ModelQualityAppSpecification", "ModelQualityJobInput", "ModelQualityJobOutputConfig", "JobResources", "RoleArn"], "members": {"JobDefinitionName": {}, "ModelQualityBaselineConfig": {"shape": "Spi"}, "ModelQualityAppSpecification": {"shape": "Spj"}, "ModelQualityJobInput": {"shape": "Spl"}, "ModelQualityJobOutputConfig": {"shape": "Sa3"}, "JobResources": {"shape": "Sa9"}, "NetworkConfig": {"shape": "Sae"}, "RoleArn": {}, "StoppingCondition": {"shape": "<PERSON><PERSON>"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["JobDefinitionArn"], "members": {"JobDefinitionArn": {}}}}, "CreateMonitoringSchedule": {"input": {"type": "structure", "required": ["MonitoringScheduleName", "MonitoringScheduleConfig"], "members": {"MonitoringScheduleName": {}, "MonitoringScheduleConfig": {"shape": "Spp"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["MonitoringScheduleArn"], "members": {"MonitoringScheduleArn": {}}}}, "CreateNotebookInstance": {"input": {"type": "structure", "required": ["NotebookInstanceName", "InstanceType", "RoleArn"], "members": {"NotebookInstanceName": {}, "InstanceType": {}, "SubnetId": {}, "SecurityGroupIds": {"shape": "Sas"}, "RoleArn": {}, "KmsKeyId": {}, "Tags": {"shape": "S7"}, "LifecycleConfigName": {}, "DirectInternetAccess": {}, "VolumeSizeInGB": {"type": "integer"}, "AcceleratorTypes": {"shape": "Sq6"}, "DefaultCodeRepository": {}, "AdditionalCodeRepositories": {"shape": "Sq9"}, "RootAccess": {}, "PlatformIdentifier": {}, "InstanceMetadataServiceConfiguration": {"shape": "Sqc"}}}, "output": {"type": "structure", "members": {"NotebookInstanceArn": {}}}}, "CreateNotebookInstanceLifecycleConfig": {"input": {"type": "structure", "required": ["NotebookInstanceLifecycleConfigName"], "members": {"NotebookInstanceLifecycleConfigName": {}, "OnCreate": {"shape": "Sqh"}, "OnStart": {"shape": "Sqh"}}}, "output": {"type": "structure", "members": {"NotebookInstanceLifecycleConfigArn": {}}}}, "CreateOptimizationJob": {"input": {"type": "structure", "required": ["OptimizationJobName", "RoleArn", "ModelSource", "DeploymentInstanceType", "OptimizationConfigs", "OutputConfig", "StoppingCondition"], "members": {"OptimizationJobName": {}, "RoleArn": {}, "ModelSource": {"shape": "Sqn"}, "DeploymentInstanceType": {}, "OptimizationEnvironment": {"shape": "Sqs"}, "OptimizationConfigs": {"shape": "Sqt"}, "OutputConfig": {"shape": "Sqy"}, "StoppingCondition": {"shape": "S3y"}, "Tags": {"shape": "S7"}, "VpcConfig": {"shape": "Sqz"}}}, "output": {"type": "structure", "required": ["OptimizationJobArn"], "members": {"OptimizationJobArn": {}}}}, "CreatePipeline": {"input": {"type": "structure", "required": ["PipelineName", "ClientRequestToken", "RoleArn"], "members": {"PipelineName": {}, "PipelineDisplayName": {}, "PipelineDefinition": {}, "PipelineDefinitionS3Location": {"shape": "Sr9"}, "PipelineDescription": {}, "ClientRequestToken": {"idempotencyToken": true}, "RoleArn": {}, "Tags": {"shape": "S7"}, "ParallelismConfiguration": {"shape": "Srf"}}}, "output": {"type": "structure", "members": {"PipelineArn": {}}}}, "CreatePresignedDomainUrl": {"input": {"type": "structure", "required": ["DomainId", "UserProfileName"], "members": {"DomainId": {}, "UserProfileName": {}, "SessionExpirationDurationInSeconds": {"type": "integer"}, "ExpiresInSeconds": {"type": "integer"}, "SpaceName": {}, "LandingUri": {}}}, "output": {"type": "structure", "members": {"AuthorizedUrl": {}}}}, "CreatePresignedMlflowTrackingServerUrl": {"input": {"type": "structure", "required": ["TrackingServerName"], "members": {"TrackingServerName": {}, "ExpiresInSeconds": {"type": "integer"}, "SessionExpirationDurationInSeconds": {"type": "integer"}}}, "output": {"type": "structure", "members": {"AuthorizedUrl": {}}}}, "CreatePresignedNotebookInstanceUrl": {"input": {"type": "structure", "required": ["NotebookInstanceName"], "members": {"NotebookInstanceName": {}, "SessionExpirationDurationInSeconds": {"type": "integer"}}}, "output": {"type": "structure", "members": {"AuthorizedUrl": {}}}}, "CreateProcessingJob": {"input": {"type": "structure", "required": ["ProcessingJobName", "ProcessingResources", "AppSpecification", "RoleArn"], "members": {"ProcessingInputs": {"shape": "Srv"}, "ProcessingOutputConfig": {"shape": "Ssi"}, "ProcessingJobName": {}, "ProcessingResources": {"shape": "Ssn"}, "StoppingCondition": {"shape": "Ssp"}, "AppSpecification": {"shape": "Ssr"}, "Environment": {"shape": "Sst"}, "NetworkConfig": {"shape": "Spx"}, "RoleArn": {}, "Tags": {"shape": "S7"}, "ExperimentConfig": {"shape": "Ssu"}}}, "output": {"type": "structure", "required": ["ProcessingJobArn"], "members": {"ProcessingJobArn": {}}}}, "CreateProject": {"input": {"type": "structure", "required": ["ProjectName", "ServiceCatalogProvisioningDetails"], "members": {"ProjectName": {}, "ProjectDescription": {}, "ServiceCatalogProvisioningDetails": {"shape": "Ssz"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["ProjectArn", "ProjectId"], "members": {"ProjectArn": {}, "ProjectId": {}}}}, "CreateSpace": {"input": {"type": "structure", "required": ["DomainId", "SpaceName"], "members": {"DomainId": {}, "SpaceName": {}, "Tags": {"shape": "S7"}, "SpaceSettings": {"shape": "St9"}, "OwnershipSettings": {"shape": "Stj"}, "SpaceSharingSettings": {"shape": "Stk"}, "SpaceDisplayName": {}}}, "output": {"type": "structure", "members": {"SpaceArn": {}}}}, "CreateStudioLifecycleConfig": {"input": {"type": "structure", "required": ["StudioLifecycleConfigName", "StudioLifecycleConfigContent", "StudioLifecycleConfigAppType"], "members": {"StudioLifecycleConfigName": {}, "StudioLifecycleConfigContent": {}, "StudioLifecycleConfigAppType": {}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "members": {"StudioLifecycleConfigArn": {}}}}, "CreateTrainingJob": {"input": {"type": "structure", "required": ["TrainingJobName", "AlgorithmSpecification", "RoleArn", "OutputDataConfig", "ResourceConfig", "StoppingCondition"], "members": {"TrainingJobName": {}, "HyperParameters": {"shape": "S35"}, "AlgorithmSpecification": {"shape": "Stv"}, "RoleArn": {}, "InputDataConfig": {"shape": "S37"}, "OutputDataConfig": {"shape": "S3p"}, "ResourceConfig": {"shape": "S3s"}, "VpcConfig": {"shape": "S6d"}, "StoppingCondition": {"shape": "S3y"}, "Tags": {"shape": "S7"}, "EnableNetworkIsolation": {"type": "boolean"}, "EnableInterContainerTrafficEncryption": {"type": "boolean"}, "EnableManagedSpotTraining": {"type": "boolean"}, "CheckpointConfig": {"shape": "Sj3"}, "DebugHookConfig": {"shape": "Su4"}, "DebugRuleConfigurations": {"shape": "Suc"}, "TensorBoardOutputConfig": {"shape": "<PERSON><PERSON>"}, "ExperimentConfig": {"shape": "Ssu"}, "ProfilerConfig": {"shape": "<PERSON><PERSON>"}, "ProfilerRuleConfigurations": {"shape": "Sul"}, "Environment": {"shape": "Sun"}, "RetryStrategy": {"shape": "Sj4"}, "RemoteDebugConfig": {"shape": "Suq"}, "InfraCheckConfig": {"shape": "<PERSON><PERSON>"}, "SessionChainingConfig": {"type": "structure", "members": {"EnableSessionTagChaining": {"type": "boolean"}}}}}, "output": {"type": "structure", "required": ["TrainingJobArn"], "members": {"TrainingJobArn": {}}}}, "CreateTransformJob": {"input": {"type": "structure", "required": ["TransformJobName", "ModelName", "TransformInput", "TransformOutput", "TransformResources"], "members": {"TransformJobName": {}, "ModelName": {}, "MaxConcurrentTransforms": {"type": "integer"}, "ModelClientConfig": {"shape": "Sv0"}, "MaxPayloadInMB": {"type": "integer"}, "BatchStrategy": {}, "Environment": {"shape": "S46"}, "TransformInput": {"shape": "S49"}, "TransformOutput": {"shape": "S4d"}, "DataCaptureConfig": {"shape": "Sv3"}, "TransformResources": {"shape": "S4g"}, "DataProcessing": {"shape": "Sv4"}, "Tags": {"shape": "S7"}, "ExperimentConfig": {"shape": "Ssu"}}}, "output": {"type": "structure", "required": ["TransformJobArn"], "members": {"TransformJobArn": {}}}}, "CreateTrial": {"input": {"type": "structure", "required": ["TrialName", "ExperimentName"], "members": {"TrialName": {}, "DisplayName": {}, "ExperimentName": {}, "MetadataProperties": {"shape": "S23"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "members": {"TrialArn": {}}}}, "CreateTrialComponent": {"input": {"type": "structure", "required": ["TrialComponentName"], "members": {"TrialComponentName": {}, "DisplayName": {}, "Status": {"shape": "Svc"}, "StartTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "Parameters": {"shape": "Svf"}, "InputArtifacts": {"shape": "Svj"}, "OutputArtifacts": {"shape": "Svj"}, "MetadataProperties": {"shape": "S23"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "members": {"TrialComponentArn": {}}}}, "CreateUserProfile": {"input": {"type": "structure", "required": ["DomainId", "UserProfileName"], "members": {"DomainId": {}, "UserProfileName": {}, "SingleSignOnUserIdentifier": {}, "SingleSignOnUserValue": {}, "Tags": {"shape": "S7"}, "UserSettings": {"shape": "Sar"}}}, "output": {"type": "structure", "members": {"UserProfileArn": {}}}}, "CreateWorkforce": {"input": {"type": "structure", "required": ["WorkforceName"], "members": {"CognitoConfig": {"shape": "Svu"}, "OidcConfig": {"shape": "Svx"}, "SourceIpConfig": {"shape": "Sw4"}, "WorkforceName": {}, "Tags": {"shape": "S7"}, "WorkforceVpcConfig": {"shape": "Sw8"}}}, "output": {"type": "structure", "required": ["WorkforceArn"], "members": {"WorkforceArn": {}}}}, "CreateWorkteam": {"input": {"type": "structure", "required": ["WorkteamName", "MemberDefinitions", "Description"], "members": {"WorkteamName": {}, "WorkforceName": {}, "MemberDefinitions": {"shape": "Swi"}, "Description": {}, "NotificationConfiguration": {"shape": "Swq"}, "WorkerAccessConfiguration": {"shape": "Sws"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "members": {"WorkteamArn": {}}}}, "DeleteAction": {"input": {"type": "structure", "required": ["ActionName"], "members": {"ActionName": {}}}, "output": {"type": "structure", "members": {"ActionArn": {}}}}, "DeleteAlgorithm": {"input": {"type": "structure", "required": ["AlgorithmName"], "members": {"AlgorithmName": {}}}}, "DeleteApp": {"input": {"type": "structure", "required": ["DomainId", "AppType", "AppName"], "members": {"DomainId": {}, "UserProfileName": {}, "SpaceName": {}, "AppType": {}, "AppName": {}}}}, "DeleteAppImageConfig": {"input": {"type": "structure", "required": ["AppImageConfigName"], "members": {"AppImageConfigName": {}}}}, "DeleteArtifact": {"input": {"type": "structure", "members": {"ArtifactArn": {}, "Source": {"shape": "S5l"}}}, "output": {"type": "structure", "members": {"ArtifactArn": {}}}}, "DeleteAssociation": {"input": {"type": "structure", "required": ["SourceArn", "DestinationArn"], "members": {"SourceArn": {}, "DestinationArn": {}}}, "output": {"type": "structure", "members": {"SourceArn": {}, "DestinationArn": {}}}}, "DeleteCluster": {"input": {"type": "structure", "required": ["ClusterName"], "members": {"ClusterName": {}}}, "output": {"type": "structure", "required": ["ClusterArn"], "members": {"ClusterArn": {}}}}, "DeleteCodeRepository": {"input": {"type": "structure", "required": ["CodeRepositoryName"], "members": {"CodeRepositoryName": {}}}}, "DeleteCompilationJob": {"input": {"type": "structure", "required": ["CompilationJobName"], "members": {"CompilationJobName": {}}}}, "DeleteContext": {"input": {"type": "structure", "required": ["ContextName"], "members": {"ContextName": {}}}, "output": {"type": "structure", "members": {"ContextArn": {}}}}, "DeleteDataQualityJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName"], "members": {"JobDefinitionName": {}}}}, "DeleteDeviceFleet": {"input": {"type": "structure", "required": ["DeviceFleetName"], "members": {"DeviceFleetName": {}}}}, "DeleteDomain": {"input": {"type": "structure", "required": ["DomainId"], "members": {"DomainId": {}, "RetentionPolicy": {"type": "structure", "members": {"HomeEfsFileSystem": {}}}}}}, "DeleteEdgeDeploymentPlan": {"input": {"type": "structure", "required": ["EdgeDeploymentPlanName"], "members": {"EdgeDeploymentPlanName": {}}}}, "DeleteEdgeDeploymentStage": {"input": {"type": "structure", "required": ["EdgeDeploymentPlanName", "StageName"], "members": {"EdgeDeploymentPlanName": {}, "StageName": {}}}}, "DeleteEndpoint": {"input": {"type": "structure", "required": ["EndpointName"], "members": {"EndpointName": {}}}}, "DeleteEndpointConfig": {"input": {"type": "structure", "required": ["EndpointConfigName"], "members": {"EndpointConfigName": {}}}}, "DeleteExperiment": {"input": {"type": "structure", "required": ["ExperimentName"], "members": {"ExperimentName": {}}}, "output": {"type": "structure", "members": {"ExperimentArn": {}}}}, "DeleteFeatureGroup": {"input": {"type": "structure", "required": ["FeatureGroupName"], "members": {"FeatureGroupName": {}}}}, "DeleteFlowDefinition": {"input": {"type": "structure", "required": ["FlowDefinitionName"], "members": {"FlowDefinitionName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteHub": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"HubName": {}}}}, "DeleteHubContent": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "HubContentType", "HubContentName", "HubContentVersion"], "members": {"HubName": {}, "HubContentType": {}, "HubContentName": {}, "HubContentVersion": {}}}}, "DeleteHubContentReference": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "HubContentType", "HubContentName"], "members": {"HubName": {}, "HubContentType": {}, "HubContentName": {}}}}, "DeleteHumanTaskUi": {"input": {"type": "structure", "required": ["HumanTaskUiName"], "members": {"HumanTaskUiName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteHyperParameterTuningJob": {"input": {"type": "structure", "required": ["HyperParameterTuningJobName"], "members": {"HyperParameterTuningJobName": {}}}}, "DeleteImage": {"input": {"type": "structure", "required": ["ImageName"], "members": {"ImageName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteImageVersion": {"input": {"type": "structure", "required": ["ImageName"], "members": {"ImageName": {}, "Version": {"type": "integer"}, "Alias": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteInferenceComponent": {"input": {"type": "structure", "required": ["InferenceComponentName"], "members": {"InferenceComponentName": {}}}}, "DeleteInferenceExperiment": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "required": ["InferenceExperimentArn"], "members": {"InferenceExperimentArn": {}}}}, "DeleteMlflowTrackingServer": {"input": {"type": "structure", "required": ["TrackingServerName"], "members": {"TrackingServerName": {}}}, "output": {"type": "structure", "members": {"TrackingServerArn": {}}}}, "DeleteModel": {"input": {"type": "structure", "required": ["ModelName"], "members": {"ModelName": {}}}}, "DeleteModelBiasJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName"], "members": {"JobDefinitionName": {}}}}, "DeleteModelCard": {"input": {"type": "structure", "required": ["ModelCardName"], "members": {"ModelCardName": {}}}}, "DeleteModelExplainabilityJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName"], "members": {"JobDefinitionName": {}}}}, "DeleteModelPackage": {"input": {"type": "structure", "required": ["ModelPackageName"], "members": {"ModelPackageName": {}}}}, "DeleteModelPackageGroup": {"input": {"type": "structure", "required": ["ModelPackageGroupName"], "members": {"ModelPackageGroupName": {}}}}, "DeleteModelPackageGroupPolicy": {"input": {"type": "structure", "required": ["ModelPackageGroupName"], "members": {"ModelPackageGroupName": {}}}}, "DeleteModelQualityJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName"], "members": {"JobDefinitionName": {}}}}, "DeleteMonitoringSchedule": {"input": {"type": "structure", "required": ["MonitoringScheduleName"], "members": {"MonitoringScheduleName": {}}}}, "DeleteNotebookInstance": {"input": {"type": "structure", "required": ["NotebookInstanceName"], "members": {"NotebookInstanceName": {}}}}, "DeleteNotebookInstanceLifecycleConfig": {"input": {"type": "structure", "required": ["NotebookInstanceLifecycleConfigName"], "members": {"NotebookInstanceLifecycleConfigName": {}}}}, "DeleteOptimizationJob": {"input": {"type": "structure", "required": ["OptimizationJobName"], "members": {"OptimizationJobName": {}}}}, "DeletePipeline": {"input": {"type": "structure", "required": ["PipelineName", "ClientRequestToken"], "members": {"PipelineName": {}, "ClientRequestToken": {"idempotencyToken": true}}}, "output": {"type": "structure", "members": {"PipelineArn": {}}}}, "DeleteProject": {"input": {"type": "structure", "required": ["ProjectName"], "members": {"ProjectName": {}}}}, "DeleteSpace": {"input": {"type": "structure", "required": ["DomainId", "SpaceName"], "members": {"DomainId": {}, "SpaceName": {}}}}, "DeleteStudioLifecycleConfig": {"input": {"type": "structure", "required": ["StudioLifecycleConfigName"], "members": {"StudioLifecycleConfigName": {}}}}, "DeleteTags": {"input": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {}, "TagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "DeleteTrial": {"input": {"type": "structure", "required": ["TrialName"], "members": {"TrialName": {}}}, "output": {"type": "structure", "members": {"TrialArn": {}}}}, "DeleteTrialComponent": {"input": {"type": "structure", "required": ["TrialComponentName"], "members": {"TrialComponentName": {}}}, "output": {"type": "structure", "members": {"TrialComponentArn": {}}}}, "DeleteUserProfile": {"input": {"type": "structure", "required": ["DomainId", "UserProfileName"], "members": {"DomainId": {}, "UserProfileName": {}}}}, "DeleteWorkforce": {"input": {"type": "structure", "required": ["WorkforceName"], "members": {"WorkforceName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteWorkteam": {"input": {"type": "structure", "required": ["WorkteamName"], "members": {"WorkteamName": {}}}, "output": {"type": "structure", "required": ["Success"], "members": {"Success": {"type": "boolean"}}}}, "DeregisterDevices": {"input": {"type": "structure", "required": ["DeviceFleetName", "DeviceNames"], "members": {"DeviceFleetName": {}, "DeviceNames": {"shape": "Sd0"}}}}, "DescribeAction": {"input": {"type": "structure", "required": ["ActionName"], "members": {"ActionName": {}}}, "output": {"type": "structure", "members": {"ActionName": {}, "ActionArn": {}, "Source": {"shape": "S1w"}, "ActionType": {}, "Description": {}, "Status": {}, "Properties": {"shape": "S21"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "MetadataProperties": {"shape": "S23"}, "LineageGroupArn": {}}}}, "DescribeAlgorithm": {"input": {"type": "structure", "required": ["AlgorithmName"], "members": {"AlgorithmName": {}}}, "output": {"type": "structure", "required": ["AlgorithmName", "AlgorithmArn", "CreationTime", "TrainingSpecification", "AlgorithmStatus", "AlgorithmStatusDetails"], "members": {"AlgorithmName": {}, "AlgorithmArn": {}, "AlgorithmDescription": {}, "CreationTime": {"type": "timestamp"}, "TrainingSpecification": {"shape": "S28"}, "InferenceSpecification": {"shape": "<PERSON>"}, "ValidationSpecification": {"shape": "S30"}, "AlgorithmStatus": {}, "AlgorithmStatusDetails": {"type": "structure", "members": {"ValidationStatuses": {"shape": "Szc"}, "ImageScanStatuses": {"shape": "Szc"}}}, "ProductId": {}, "CertifyForMarketplace": {"type": "boolean"}}}}, "DescribeApp": {"input": {"type": "structure", "required": ["DomainId", "AppType", "AppName"], "members": {"DomainId": {}, "UserProfileName": {}, "SpaceName": {}, "AppType": {}, "AppName": {}}}, "output": {"type": "structure", "members": {"AppArn": {}, "AppType": {}, "AppName": {}, "DomainId": {}, "UserProfileName": {}, "SpaceName": {}, "Status": {}, "LastHealthCheckTimestamp": {"type": "timestamp"}, "LastUserActivityTimestamp": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "FailureReason": {}, "ResourceSpec": {"shape": "S4r"}}}}, "DescribeAppImageConfig": {"input": {"type": "structure", "required": ["AppImageConfigName"], "members": {"AppImageConfigName": {}}}, "output": {"type": "structure", "members": {"AppImageConfigArn": {}, "AppImageConfigName": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "KernelGatewayImageConfig": {"shape": "S51"}, "JupyterLabAppImageConfig": {"shape": "S5a"}, "CodeEditorAppImageConfig": {"shape": "S5h"}}}}, "DescribeArtifact": {"input": {"type": "structure", "required": ["ArtifactArn"], "members": {"ArtifactArn": {}}}, "output": {"type": "structure", "members": {"ArtifactName": {}, "ArtifactArn": {}, "Source": {"shape": "S5l"}, "ArtifactType": {}, "Properties": {"shape": "S21"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "MetadataProperties": {"shape": "S23"}, "LineageGroupArn": {}}}}, "DescribeAutoMLJob": {"input": {"type": "structure", "required": ["AutoMLJobName"], "members": {"AutoMLJobName": {}}}, "output": {"type": "structure", "required": ["AutoMLJobName", "AutoMLJobArn", "InputDataConfig", "OutputDataConfig", "RoleArn", "CreationTime", "LastModifiedTime", "AutoMLJobStatus", "AutoMLJobSecondaryStatus"], "members": {"AutoMLJobName": {}, "AutoMLJobArn": {}, "InputDataConfig": {"shape": "S5v"}, "OutputDataConfig": {"shape": "S63"}, "RoleArn": {}, "AutoMLJobObjective": {"shape": "S65"}, "ProblemType": {}, "AutoMLJobConfig": {"shape": "S67"}, "CreationTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}, "PartialFailureReasons": {"shape": "Szq"}, "BestCandidate": {"shape": "Szs"}, "AutoMLJobStatus": {}, "AutoMLJobSecondaryStatus": {}, "GenerateCandidateDefinitionsOnly": {"type": "boolean"}, "AutoMLJobArtifacts": {"shape": "S10k"}, "ResolvedAttributes": {"type": "structure", "members": {"AutoMLJobObjective": {"shape": "S65"}, "ProblemType": {}, "CompletionCriteria": {"shape": "S68"}}}, "ModelDeployConfig": {"shape": "S6r"}, "ModelDeployResult": {"shape": "S10o"}}}}, "DescribeAutoMLJobV2": {"input": {"type": "structure", "required": ["AutoMLJobName"], "members": {"AutoMLJobName": {}}}, "output": {"type": "structure", "required": ["AutoMLJobName", "AutoMLJobArn", "AutoMLJobInputDataConfig", "OutputDataConfig", "RoleArn", "CreationTime", "LastModifiedTime", "AutoMLJobStatus", "AutoMLJobSecondaryStatus"], "members": {"AutoMLJobName": {}, "AutoMLJobArn": {}, "AutoMLJobInputDataConfig": {"shape": "S6x"}, "OutputDataConfig": {"shape": "S63"}, "RoleArn": {}, "AutoMLJobObjective": {"shape": "S65"}, "AutoMLProblemTypeConfig": {"shape": "S6z"}, "AutoMLProblemTypeConfigName": {}, "CreationTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}, "PartialFailureReasons": {"shape": "Szq"}, "BestCandidate": {"shape": "Szs"}, "AutoMLJobStatus": {}, "AutoMLJobSecondaryStatus": {}, "AutoMLJobArtifacts": {"shape": "S10k"}, "ResolvedAttributes": {"type": "structure", "members": {"AutoMLJobObjective": {"shape": "S65"}, "CompletionCriteria": {"shape": "S68"}, "AutoMLProblemTypeResolvedAttributes": {"type": "structure", "members": {"TabularResolvedAttributes": {"type": "structure", "members": {"ProblemType": {}}}, "TextGenerationResolvedAttributes": {"type": "structure", "members": {"BaseModelName": {}}}}, "union": true}}}, "ModelDeployConfig": {"shape": "S6r"}, "ModelDeployResult": {"shape": "S10o"}, "DataSplitConfig": {"shape": "S6n"}, "SecurityConfig": {"shape": "S6c"}, "AutoMLComputeConfig": {"shape": "S7w"}}}}, "DescribeCluster": {"input": {"type": "structure", "required": ["ClusterName"], "members": {"ClusterName": {}}}, "output": {"type": "structure", "required": ["ClusterArn", "ClusterStatus", "InstanceGroups"], "members": {"ClusterArn": {}, "ClusterName": {}, "ClusterStatus": {}, "CreationTime": {"type": "timestamp"}, "FailureMessage": {}, "InstanceGroups": {"type": "list", "member": {"type": "structure", "members": {"CurrentCount": {"type": "integer"}, "TargetCount": {"type": "integer"}, "InstanceGroupName": {}, "InstanceType": {}, "LifeCycleConfig": {"shape": "S86"}, "ExecutionRole": {}, "ThreadsPerCore": {"type": "integer"}, "InstanceStorageConfigs": {"shape": "S8a"}}}}, "VpcConfig": {"shape": "S6d"}}}}, "DescribeClusterNode": {"input": {"type": "structure", "required": ["ClusterName", "NodeId"], "members": {"ClusterName": {}, "NodeId": {}}}, "output": {"type": "structure", "required": ["NodeDetails"], "members": {"NodeDetails": {"type": "structure", "members": {"InstanceGroupName": {}, "InstanceId": {}, "InstanceStatus": {"shape": "S116"}, "InstanceType": {}, "LaunchTime": {"type": "timestamp"}, "LifeCycleConfig": {"shape": "S86"}, "ThreadsPerCore": {"type": "integer"}, "InstanceStorageConfigs": {"shape": "S8a"}, "PrivatePrimaryIp": {}, "PrivateDnsHostname": {}, "Placement": {"type": "structure", "members": {"AvailabilityZone": {}, "AvailabilityZoneId": {}}}}}}}}, "DescribeCodeRepository": {"input": {"type": "structure", "required": ["CodeRepositoryName"], "members": {"CodeRepositoryName": {}}}, "output": {"type": "structure", "required": ["CodeRepositoryName", "CodeRepositoryArn", "CreationTime", "LastModifiedTime"], "members": {"CodeRepositoryName": {}, "CodeRepositoryArn": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "GitConfig": {"shape": "S8h"}}}}, "DescribeCompilationJob": {"input": {"type": "structure", "required": ["CompilationJobName"], "members": {"CompilationJobName": {}}}, "output": {"type": "structure", "required": ["CompilationJobName", "CompilationJobArn", "CompilationJobStatus", "StoppingCondition", "CreationTime", "LastModifiedTime", "FailureReason", "ModelArtifacts", "RoleArn", "InputConfig", "OutputConfig"], "members": {"CompilationJobName": {}, "CompilationJobArn": {}, "CompilationJobStatus": {}, "CompilationStartTime": {"type": "timestamp"}, "CompilationEndTime": {"type": "timestamp"}, "StoppingCondition": {"shape": "S3y"}, "InferenceImage": {}, "ModelPackageVersionArn": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}, "ModelArtifacts": {"shape": "S11k"}, "ModelDigests": {"type": "structure", "members": {"ArtifactDigest": {}}}, "RoleArn": {}, "InputConfig": {"shape": "S8o"}, "OutputConfig": {"shape": "S8r"}, "VpcConfig": {"shape": "S8y"}, "DerivedInformation": {"type": "structure", "members": {"DerivedDataInputConfig": {}}}}}}, "DescribeContext": {"input": {"type": "structure", "required": ["ContextName"], "members": {"ContextName": {}}}, "output": {"type": "structure", "members": {"ContextName": {}, "ContextArn": {}, "Source": {"shape": "S97"}, "ContextType": {}, "Description": {}, "Properties": {"shape": "S21"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "LineageGroupArn": {}}}}, "DescribeDataQualityJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName"], "members": {"JobDefinitionName": {}}}, "output": {"type": "structure", "required": ["JobDefinitionArn", "JobDefinitionName", "CreationTime", "DataQualityAppSpecification", "DataQualityJobInput", "DataQualityJobOutputConfig", "JobResources", "RoleArn"], "members": {"JobDefinitionArn": {}, "JobDefinitionName": {}, "CreationTime": {"type": "timestamp"}, "DataQualityBaselineConfig": {"shape": "S9c"}, "DataQualityAppSpecification": {"shape": "S9g"}, "DataQualityJobInput": {"shape": "S9p"}, "DataQualityJobOutputConfig": {"shape": "Sa3"}, "JobResources": {"shape": "Sa9"}, "NetworkConfig": {"shape": "Sae"}, "RoleArn": {}, "StoppingCondition": {"shape": "<PERSON><PERSON>"}}}}, "DescribeDevice": {"input": {"type": "structure", "required": ["DeviceName", "DeviceFleetName"], "members": {"NextToken": {}, "DeviceName": {}, "DeviceFleetName": {}}}, "output": {"type": "structure", "required": ["DeviceName", "DeviceFleetName", "RegistrationTime"], "members": {"DeviceArn": {}, "DeviceName": {}, "Description": {}, "DeviceFleetName": {}, "IotThingName": {}, "RegistrationTime": {"type": "timestamp"}, "LatestHeartbeat": {"type": "timestamp"}, "Models": {"type": "list", "member": {"type": "structure", "required": ["ModelName", "ModelVersion"], "members": {"ModelName": {}, "ModelVersion": {}, "LatestSampleTime": {"type": "timestamp"}, "LatestInference": {"type": "timestamp"}}}}, "MaxModels": {"type": "integer"}, "NextToken": {}, "AgentVersion": {}}}}, "DescribeDeviceFleet": {"input": {"type": "structure", "required": ["DeviceFleetName"], "members": {"DeviceFleetName": {}}}, "output": {"type": "structure", "required": ["DeviceFleetName", "DeviceFleetArn", "OutputConfig", "CreationTime", "LastModifiedTime"], "members": {"DeviceFleetName": {}, "DeviceFleetArn": {}, "OutputConfig": {"shape": "Sal"}, "Description": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "RoleArn": {}, "IotRoleAlias": {}}}}, "DescribeDomain": {"input": {"type": "structure", "required": ["DomainId"], "members": {"DomainId": {}}}, "output": {"type": "structure", "members": {"DomainArn": {}, "DomainId": {}, "DomainName": {}, "HomeEfsFileSystemId": {}, "SingleSignOnManagedApplicationInstanceId": {}, "SingleSignOnApplicationArn": {}, "Status": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}, "SecurityGroupIdForDomainBoundary": {}, "AuthMode": {}, "DefaultUserSettings": {"shape": "Sar"}, "DomainSettings": {"shape": "Scc"}, "AppNetworkAccessType": {}, "HomeEfsFileSystemKmsKeyId": {"deprecated": true, "deprecatedMessage": "This property is deprecated, use KmsKeyId instead."}, "SubnetIds": {"shape": "S6g"}, "Url": {}, "VpcId": {}, "KmsKeyId": {}, "AppSecurityGroupManagement": {}, "DefaultSpaceSettings": {"shape": "Sco"}}}}, "DescribeEdgeDeploymentPlan": {"input": {"type": "structure", "required": ["EdgeDeploymentPlanName"], "members": {"EdgeDeploymentPlanName": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["EdgeDeploymentPlanArn", "EdgeDeploymentPlanName", "ModelConfigs", "DeviceFleetName", "Stages"], "members": {"EdgeDeploymentPlanArn": {}, "EdgeDeploymentPlanName": {}, "ModelConfigs": {"shape": "Sct"}, "DeviceFleetName": {}, "EdgeDeploymentSuccess": {"type": "integer"}, "EdgeDeploymentPending": {"type": "integer"}, "EdgeDeploymentFailed": {"type": "integer"}, "Stages": {"type": "list", "member": {"type": "structure", "required": ["StageName", "DeviceSelectionConfig", "DeploymentConfig", "DeploymentStatus"], "members": {"StageName": {}, "DeviceSelectionConfig": {"shape": "Scx"}, "DeploymentConfig": {"shape": "Sd2"}, "DeploymentStatus": {"type": "structure", "required": ["StageStatus", "EdgeDeploymentSuccessInStage", "EdgeDeploymentPendingInStage", "EdgeDeploymentFailedInStage"], "members": {"StageStatus": {}, "EdgeDeploymentSuccessInStage": {"type": "integer"}, "EdgeDeploymentPendingInStage": {"type": "integer"}, "EdgeDeploymentFailedInStage": {"type": "integer"}, "EdgeDeploymentStatusMessage": {}, "EdgeDeploymentStageStartTime": {"type": "timestamp"}}}}}}, "NextToken": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "DescribeEdgePackagingJob": {"input": {"type": "structure", "required": ["EdgePackagingJobName"], "members": {"EdgePackagingJobName": {}}}, "output": {"type": "structure", "required": ["EdgePackagingJobArn", "EdgePackagingJobName", "EdgePackagingJobStatus"], "members": {"EdgePackagingJobArn": {}, "EdgePackagingJobName": {}, "CompilationJobName": {}, "ModelName": {}, "ModelVersion": {}, "RoleArn": {}, "OutputConfig": {"shape": "Sal"}, "ResourceKey": {}, "EdgePackagingJobStatus": {}, "EdgePackagingJobStatusMessage": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "ModelArtifact": {}, "ModelSignature": {}, "PresetDeploymentOutput": {"type": "structure", "required": ["Type"], "members": {"Type": {}, "Artifact": {}, "Status": {}, "StatusMessage": {}}}}}}, "DescribeEndpoint": {"input": {"type": "structure", "required": ["EndpointName"], "members": {"EndpointName": {}}}, "output": {"type": "structure", "required": ["EndpointName", "EndpointArn", "EndpointStatus", "CreationTime", "LastModifiedTime"], "members": {"EndpointName": {}, "EndpointArn": {}, "EndpointConfigName": {}, "ProductionVariants": {"shape": "S12q"}, "DataCaptureConfig": {"shape": "S12y"}, "EndpointStatus": {}, "FailureReason": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "LastDeploymentConfig": {"shape": "Sdb"}, "AsyncInferenceConfig": {"shape": "Ser"}, "PendingDeploymentSummary": {"type": "structure", "required": ["EndpointConfigName"], "members": {"EndpointConfigName": {}, "ProductionVariants": {"shape": "S132"}, "StartTime": {"type": "timestamp"}, "ShadowProductionVariants": {"shape": "S132"}}}, "ExplainerConfig": {"shape": "Sez"}, "ShadowProductionVariants": {"shape": "S12q"}}}}, "DescribeEndpointConfig": {"input": {"type": "structure", "required": ["EndpointConfigName"], "members": {"EndpointConfigName": {}}}, "output": {"type": "structure", "required": ["EndpointConfigName", "EndpointConfigArn", "ProductionVariants", "CreationTime"], "members": {"EndpointConfigName": {}, "EndpointConfigArn": {}, "ProductionVariants": {"shape": "Sdt"}, "DataCaptureConfig": {"shape": "Seg"}, "KmsKeyId": {}, "CreationTime": {"type": "timestamp"}, "AsyncInferenceConfig": {"shape": "Ser"}, "ExplainerConfig": {"shape": "Sez"}, "ShadowProductionVariants": {"shape": "Sdt"}, "ExecutionRoleArn": {}, "VpcConfig": {"shape": "S6d"}, "EnableNetworkIsolation": {"type": "boolean"}}}}, "DescribeExperiment": {"input": {"type": "structure", "required": ["ExperimentName"], "members": {"ExperimentName": {}}}, "output": {"type": "structure", "members": {"ExperimentName": {}, "ExperimentArn": {}, "DisplayName": {}, "Source": {"shape": "S138"}, "Description": {}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}}}}, "DescribeFeatureGroup": {"input": {"type": "structure", "required": ["FeatureGroupName"], "members": {"FeatureGroupName": {}, "NextToken": {}}}, "output": {"type": "structure", "required": ["FeatureGroupArn", "FeatureGroupName", "RecordIdentifierFeatureName", "EventTimeFeatureName", "FeatureDefinitions", "CreationTime", "NextToken"], "members": {"FeatureGroupArn": {}, "FeatureGroupName": {}, "RecordIdentifierFeatureName": {}, "EventTimeFeatureName": {}, "FeatureDefinitions": {"shape": "Sfy"}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "OnlineStoreConfig": {"shape": "Sg5"}, "OfflineStoreConfig": {"shape": "Sgb"}, "ThroughputConfig": {"type": "structure", "required": ["ThroughputMode"], "members": {"ThroughputMode": {}, "ProvisionedReadCapacityUnits": {"type": "integer"}, "ProvisionedWriteCapacityUnits": {"type": "integer"}}}, "RoleArn": {}, "FeatureGroupStatus": {}, "OfflineStoreStatus": {"shape": "S13g"}, "LastUpdateStatus": {"shape": "S13j"}, "FailureReason": {}, "Description": {}, "NextToken": {}, "OnlineStoreTotalSizeBytes": {"type": "long"}}}}, "DescribeFeatureMetadata": {"input": {"type": "structure", "required": ["FeatureGroupName", "FeatureName"], "members": {"FeatureGroupName": {}, "FeatureName": {}}}, "output": {"type": "structure", "required": ["FeatureGroupArn", "FeatureGroupName", "FeatureName", "FeatureType", "CreationTime", "LastModifiedTime"], "members": {"FeatureGroupArn": {}, "FeatureGroupName": {}, "FeatureName": {}, "FeatureType": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "Description": {}, "Parameters": {"shape": "S13p"}}}}, "DescribeFlowDefinition": {"input": {"type": "structure", "required": ["FlowDefinitionName"], "members": {"FlowDefinitionName": {}}}, "output": {"type": "structure", "required": ["FlowDefinitionArn", "FlowDefinitionName", "FlowDefinitionStatus", "CreationTime", "OutputConfig", "RoleArn"], "members": {"FlowDefinitionArn": {}, "FlowDefinitionName": {}, "FlowDefinitionStatus": {}, "CreationTime": {"type": "timestamp"}, "HumanLoopRequestSource": {"shape": "Sgq"}, "HumanLoopActivationConfig": {"shape": "Sgs"}, "HumanLoopConfig": {"shape": "Sgv"}, "OutputConfig": {"shape": "<PERSON><PERSON>"}, "RoleArn": {}, "FailureReason": {}}}}, "DescribeHub": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"HubName": {}}}, "output": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>b<PERSON><PERSON><PERSON>", "CreationTime", "LastModifiedTime"], "members": {"HubName": {}, "HubArn": {}, "HubDisplayName": {}, "HubDescription": {}, "HubSearchKeywords": {"shape": "Shh"}, "S3StorageConfig": {"shape": "Shj"}, "HubStatus": {}, "FailureReason": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "DescribeHubContent": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "HubContentType", "HubContentName"], "members": {"HubName": {}, "HubContentType": {}, "HubContentName": {}, "HubContentVersion": {}}}, "output": {"type": "structure", "required": ["HubContentName", "HubContentArn", "HubContentVersion", "HubContentType", "DocumentSchemaVersion", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "HubContentDocument", "HubContentStatus", "CreationTime"], "members": {"HubContentName": {}, "HubContentArn": {}, "HubContentVersion": {}, "HubContentType": {}, "DocumentSchemaVersion": {}, "HubName": {}, "HubArn": {}, "HubContentDisplayName": {}, "HubContentDescription": {}, "HubContentMarkdown": {}, "HubContentDocument": {}, "SageMakerPublicHubContentArn": {}, "ReferenceMinVersion": {}, "SupportStatus": {}, "HubContentSearchKeywords": {"shape": "S148"}, "HubContentDependencies": {"type": "list", "member": {"type": "structure", "members": {"DependencyOriginPath": {}, "DependencyCopyPath": {}}}}, "HubContentStatus": {}, "FailureReason": {}, "CreationTime": {"type": "timestamp"}}}}, "DescribeHumanTaskUi": {"input": {"type": "structure", "required": ["HumanTaskUiName"], "members": {"HumanTaskUiName": {}}}, "output": {"type": "structure", "required": ["HumanTaskUiArn", "HumanTaskUiName", "CreationTime", "UiTemplate"], "members": {"HumanTaskUiArn": {}, "HumanTaskUiName": {}, "HumanTaskUiStatus": {}, "CreationTime": {"type": "timestamp"}, "UiTemplate": {"type": "structure", "members": {"Url": {}, "ContentSha256": {}}}}}}, "DescribeHyperParameterTuningJob": {"input": {"type": "structure", "required": ["HyperParameterTuningJobName"], "members": {"HyperParameterTuningJobName": {}}}, "output": {"type": "structure", "required": ["HyperParameterTuningJobName", "HyperParameterTuningJobArn", "HyperParameterTuningJobConfig", "HyperParameterTuningJobStatus", "CreationTime", "TrainingJobStatusCounters", "ObjectiveStatusCounters"], "members": {"HyperParameterTuningJobName": {}, "HyperParameterTuningJobArn": {}, "HyperParameterTuningJobConfig": {"shape": "Si0"}, "TrainingJobDefinition": {"shape": "Sit"}, "TrainingJobDefinitions": {"shape": "Sj9"}, "HyperParameterTuningJobStatus": {}, "CreationTime": {"type": "timestamp"}, "HyperParameterTuningEndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "TrainingJobStatusCounters": {"shape": "S14n"}, "ObjectiveStatusCounters": {"shape": "S14p"}, "BestTrainingJob": {"shape": "S14r"}, "OverallBestTrainingJob": {"shape": "S14r"}, "WarmStartConfig": {"shape": "<PERSON><PERSON>"}, "Autotune": {"shape": "<PERSON><PERSON>"}, "FailureReason": {}, "TuningJobCompletionDetails": {"shape": "S14u"}, "ConsumedResources": {"shape": "S14v"}}}}, "DescribeImage": {"input": {"type": "structure", "required": ["ImageName"], "members": {"ImageName": {}}}, "output": {"type": "structure", "members": {"CreationTime": {"type": "timestamp"}, "Description": {}, "DisplayName": {}, "FailureReason": {}, "ImageArn": {}, "ImageName": {}, "ImageStatus": {}, "LastModifiedTime": {"type": "timestamp"}, "RoleArn": {}}}}, "DescribeImageVersion": {"input": {"type": "structure", "required": ["ImageName"], "members": {"ImageName": {}, "Version": {"type": "integer"}, "Alias": {}}}, "output": {"type": "structure", "members": {"BaseImage": {}, "ContainerImage": {}, "CreationTime": {"type": "timestamp"}, "FailureReason": {}, "ImageArn": {}, "ImageVersionArn": {}, "ImageVersionStatus": {}, "LastModifiedTime": {"type": "timestamp"}, "Version": {"type": "integer"}, "VendorGuidance": {}, "JobType": {}, "MLFramework": {}, "ProgrammingLang": {}, "Processor": {}, "Horovod": {"type": "boolean"}, "ReleaseNotes": {}}}}, "DescribeInferenceComponent": {"input": {"type": "structure", "required": ["InferenceComponentName"], "members": {"InferenceComponentName": {}}}, "output": {"type": "structure", "required": ["InferenceComponentName", "InferenceComponentArn", "EndpointName", "EndpointArn", "CreationTime", "LastModifiedTime"], "members": {"InferenceComponentName": {}, "InferenceComponentArn": {}, "EndpointName": {}, "EndpointArn": {}, "VariantName": {}, "FailureReason": {}, "Specification": {"type": "structure", "members": {"ModelName": {}, "Container": {"type": "structure", "members": {"DeployedImage": {"shape": "S12t"}, "ArtifactUrl": {}, "Environment": {"shape": "S18"}}}, "StartupParameters": {"shape": "Sk3"}, "ComputeResourceRequirements": {"shape": "Sk4"}}}, "RuntimeConfig": {"type": "structure", "members": {"DesiredCopyCount": {"type": "integer"}, "CurrentCopyCount": {"type": "integer"}}}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "InferenceComponentStatus": {}}}}, "DescribeInferenceExperiment": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "required": ["<PERSON><PERSON>", "Name", "Type", "Status", "EndpointMetadata", "ModelVariants"], "members": {"Arn": {}, "Name": {}, "Type": {}, "Schedule": {"shape": "Skf"}, "Status": {}, "StatusReason": {}, "Description": {}, "CreationTime": {"type": "timestamp"}, "CompletionTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "RoleArn": {}, "EndpointMetadata": {"type": "structure", "required": ["EndpointName"], "members": {"EndpointName": {}, "EndpointConfigName": {}, "EndpointStatus": {}, "FailureReason": {}}}, "ModelVariants": {"type": "list", "member": {"type": "structure", "required": ["ModelName", "VariantName", "InfrastructureConfig", "Status"], "members": {"ModelName": {}, "VariantName": {}, "InfrastructureConfig": {"shape": "Skl"}, "Status": {}}}}, "DataStorageConfig": {"shape": "Skq"}, "ShadowModeConfig": {"shape": "Skr"}, "KmsKey": {}}}}, "DescribeInferenceRecommendationsJob": {"input": {"type": "structure", "required": ["JobName"], "members": {"JobName": {}}}, "output": {"type": "structure", "required": ["JobName", "JobType", "JobArn", "RoleArn", "Status", "CreationTime", "LastModifiedTime", "InputConfig"], "members": {"JobName": {}, "JobDescription": {}, "JobType": {}, "JobArn": {}, "RoleArn": {}, "Status": {}, "CreationTime": {"type": "timestamp"}, "CompletionTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}, "InputConfig": {"shape": "Skz"}, "StoppingConditions": {"shape": "Sm5"}, "InferenceRecommendations": {"type": "list", "member": {"type": "structure", "required": ["EndpointConfiguration", "ModelConfiguration"], "members": {"RecommendationId": {}, "Metrics": {"shape": "S15m"}, "EndpointConfiguration": {"shape": "S15p"}, "ModelConfiguration": {"shape": "S15r"}, "InvocationEndTime": {"type": "timestamp"}, "InvocationStartTime": {"type": "timestamp"}}}}, "EndpointPerformances": {"type": "list", "member": {"type": "structure", "required": ["Metrics", "EndpointInfo"], "members": {"Metrics": {"shape": "S15z"}, "EndpointInfo": {"shape": "<PERSON><PERSON>"}}}}}}}, "DescribeLabelingJob": {"input": {"type": "structure", "required": ["LabelingJobName"], "members": {"LabelingJobName": {}}}, "output": {"type": "structure", "required": ["LabelingJobStatus", "LabelCounters", "CreationTime", "LastModifiedTime", "JobReferenceCode", "LabelingJobName", "LabelingJobArn", "InputConfig", "OutputConfig", "RoleArn", "HumanTaskConfig"], "members": {"LabelingJobStatus": {}, "LabelCounters": {"shape": "S163"}, "FailureReason": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "JobReferenceCode": {}, "LabelingJobName": {}, "LabelingJobArn": {}, "LabelAttributeName": {}, "InputConfig": {"shape": "Smh"}, "OutputConfig": {"shape": "Smo"}, "RoleArn": {}, "LabelCategoryConfigS3Uri": {}, "StoppingConditions": {"shape": "Smp"}, "LabelingJobAlgorithmsConfig": {"shape": "Sms"}, "HumanTaskConfig": {"shape": "Smw"}, "Tags": {"shape": "S7"}, "LabelingJobOutput": {"shape": "S166"}}}}, "DescribeLineageGroup": {"input": {"type": "structure", "required": ["LineageGroupName"], "members": {"LineageGroupName": {}}}, "output": {"type": "structure", "members": {"LineageGroupName": {}, "LineageGroupArn": {}, "DisplayName": {}, "Description": {}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}}}}, "DescribeMlflowTrackingServer": {"input": {"type": "structure", "required": ["TrackingServerName"], "members": {"TrackingServerName": {}}}, "output": {"type": "structure", "members": {"TrackingServerArn": {}, "TrackingServerName": {}, "ArtifactStoreUri": {}, "TrackingServerSize": {}, "MlflowVersion": {}, "RoleArn": {}, "TrackingServerStatus": {}, "IsActive": {}, "TrackingServerUrl": {}, "WeeklyMaintenanceWindowStart": {}, "AutomaticModelRegistration": {"type": "boolean"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}}}}, "DescribeModel": {"input": {"type": "structure", "required": ["ModelName"], "members": {"ModelName": {}}}, "output": {"type": "structure", "required": ["ModelName", "CreationTime", "ModelArn"], "members": {"ModelName": {}, "PrimaryContainer": {"shape": "Sni"}, "Containers": {"shape": "<PERSON><PERSON>"}, "InferenceExecutionConfig": {"shape": "Snv"}, "ExecutionRoleArn": {}, "VpcConfig": {"shape": "S6d"}, "CreationTime": {"type": "timestamp"}, "ModelArn": {}, "EnableNetworkIsolation": {"type": "boolean"}, "DeploymentRecommendation": {"shape": "S16f"}}}}, "DescribeModelBiasJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName"], "members": {"JobDefinitionName": {}}}, "output": {"type": "structure", "required": ["JobDefinitionArn", "JobDefinitionName", "CreationTime", "ModelBiasAppSpecification", "ModelBiasJobInput", "ModelBiasJobOutputConfig", "JobResources", "RoleArn"], "members": {"JobDefinitionArn": {}, "JobDefinitionName": {}, "CreationTime": {"type": "timestamp"}, "ModelBiasBaselineConfig": {"shape": "Snz"}, "ModelBiasAppSpecification": {"shape": "So0"}, "ModelBiasJobInput": {"shape": "So1"}, "ModelBiasJobOutputConfig": {"shape": "Sa3"}, "JobResources": {"shape": "Sa9"}, "NetworkConfig": {"shape": "Sae"}, "RoleArn": {}, "StoppingCondition": {"shape": "<PERSON><PERSON>"}}}}, "DescribeModelCard": {"input": {"type": "structure", "required": ["ModelCardName"], "members": {"ModelCardName": {}, "ModelCardVersion": {"type": "integer"}}}, "output": {"type": "structure", "required": ["ModelCardArn", "ModelCardName", "ModelCardVersion", "Content", "ModelCardStatus", "CreationTime", "CreatedBy"], "members": {"ModelCardArn": {}, "ModelCardName": {}, "ModelCardVersion": {"type": "integer"}, "Content": {"shape": "So6"}, "ModelCardStatus": {}, "SecurityConfig": {"shape": "So5"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "ModelCardProcessingStatus": {}}}}, "DescribeModelCardExportJob": {"input": {"type": "structure", "required": ["ModelCardExportJobArn"], "members": {"ModelCardExportJobArn": {}}}, "output": {"type": "structure", "required": ["ModelCardExportJobName", "ModelCardExportJobArn", "Status", "ModelCardName", "ModelCardVersion", "OutputConfig", "CreatedAt", "LastModifiedAt"], "members": {"ModelCardExportJobName": {}, "ModelCardExportJobArn": {}, "Status": {}, "ModelCardName": {}, "ModelCardVersion": {"type": "integer"}, "OutputConfig": {"shape": "Soc"}, "CreatedAt": {"type": "timestamp"}, "LastModifiedAt": {"type": "timestamp"}, "FailureReason": {}, "ExportArtifacts": {"type": "structure", "required": ["S3ExportArtifacts"], "members": {"S3ExportArtifacts": {}}}}}}, "DescribeModelExplainabilityJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName"], "members": {"JobDefinitionName": {}}}, "output": {"type": "structure", "required": ["JobDefinitionArn", "JobDefinitionName", "CreationTime", "ModelExplainabilityAppSpecification", "ModelExplainabilityJobInput", "ModelExplainabilityJobOutputConfig", "JobResources", "RoleArn"], "members": {"JobDefinitionArn": {}, "JobDefinitionName": {}, "CreationTime": {"type": "timestamp"}, "ModelExplainabilityBaselineConfig": {"shape": "Sog"}, "ModelExplainabilityAppSpecification": {"shape": "Soh"}, "ModelExplainabilityJobInput": {"shape": "Soi"}, "ModelExplainabilityJobOutputConfig": {"shape": "Sa3"}, "JobResources": {"shape": "Sa9"}, "NetworkConfig": {"shape": "Sae"}, "RoleArn": {}, "StoppingCondition": {"shape": "<PERSON><PERSON>"}}}}, "DescribeModelPackage": {"input": {"type": "structure", "required": ["ModelPackageName"], "members": {"ModelPackageName": {}}}, "output": {"type": "structure", "required": ["ModelPackageName", "ModelPackageArn", "CreationTime", "ModelPackageStatus", "ModelPackageStatusDetails"], "members": {"ModelPackageName": {}, "ModelPackageGroupName": {}, "ModelPackageVersion": {"type": "integer"}, "ModelPackageArn": {}, "ModelPackageDescription": {}, "CreationTime": {"type": "timestamp"}, "InferenceSpecification": {"shape": "<PERSON>"}, "SourceAlgorithmSpecification": {"shape": "<PERSON>"}, "ValidationSpecification": {"shape": "Sol"}, "ModelPackageStatus": {}, "ModelPackageStatusDetails": {"shape": "S16w"}, "CertifyForMarketplace": {"type": "boolean"}, "ModelApprovalStatus": {}, "CreatedBy": {"shape": "Sz5"}, "MetadataProperties": {"shape": "S23"}, "ModelMetrics": {"shape": "Sor"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "ApprovalDescription": {}, "Domain": {}, "Task": {}, "SamplePayloadUrl": {}, "CustomerMetadataProperties": {"shape": "Soy"}, "DriftCheckBaselines": {"shape": "Sp1"}, "AdditionalInferenceSpecifications": {"shape": "Sp7"}, "SkipModelValidation": {}, "SourceUri": {}, "SecurityConfig": {"shape": "Spb"}, "ModelCard": {"shape": "Spc"}}}}, "DescribeModelPackageGroup": {"input": {"type": "structure", "required": ["ModelPackageGroupName"], "members": {"ModelPackageGroupName": {}}}, "output": {"type": "structure", "required": ["ModelPackageGroupName", "ModelPackageGroupArn", "CreationTime", "CreatedBy", "ModelPackageGroupStatus"], "members": {"ModelPackageGroupName": {}, "ModelPackageGroupArn": {}, "ModelPackageGroupDescription": {}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "ModelPackageGroupStatus": {}}}}, "DescribeModelQualityJobDefinition": {"input": {"type": "structure", "required": ["JobDefinitionName"], "members": {"JobDefinitionName": {}}}, "output": {"type": "structure", "required": ["JobDefinitionArn", "JobDefinitionName", "CreationTime", "ModelQualityAppSpecification", "ModelQualityJobInput", "ModelQualityJobOutputConfig", "JobResources", "RoleArn"], "members": {"JobDefinitionArn": {}, "JobDefinitionName": {}, "CreationTime": {"type": "timestamp"}, "ModelQualityBaselineConfig": {"shape": "Spi"}, "ModelQualityAppSpecification": {"shape": "Spj"}, "ModelQualityJobInput": {"shape": "Spl"}, "ModelQualityJobOutputConfig": {"shape": "Sa3"}, "JobResources": {"shape": "Sa9"}, "NetworkConfig": {"shape": "Sae"}, "RoleArn": {}, "StoppingCondition": {"shape": "<PERSON><PERSON>"}}}}, "DescribeMonitoringSchedule": {"input": {"type": "structure", "required": ["MonitoringScheduleName"], "members": {"MonitoringScheduleName": {}}}, "output": {"type": "structure", "required": ["MonitoringScheduleArn", "MonitoringScheduleName", "MonitoringScheduleStatus", "CreationTime", "LastModifiedTime", "MonitoringScheduleConfig"], "members": {"MonitoringScheduleArn": {}, "MonitoringScheduleName": {}, "MonitoringScheduleStatus": {}, "MonitoringType": {}, "FailureReason": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "MonitoringScheduleConfig": {"shape": "Spp"}, "EndpointName": {}, "LastMonitoringExecutionSummary": {"shape": "S179"}}}}, "DescribeNotebookInstance": {"input": {"type": "structure", "required": ["NotebookInstanceName"], "members": {"NotebookInstanceName": {}}}, "output": {"type": "structure", "members": {"NotebookInstanceArn": {}, "NotebookInstanceName": {}, "NotebookInstanceStatus": {}, "FailureReason": {}, "Url": {}, "InstanceType": {}, "SubnetId": {}, "SecurityGroups": {"shape": "Sas"}, "RoleArn": {}, "KmsKeyId": {}, "NetworkInterfaceId": {}, "LastModifiedTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "NotebookInstanceLifecycleConfigName": {}, "DirectInternetAccess": {}, "VolumeSizeInGB": {"type": "integer"}, "AcceleratorTypes": {"shape": "Sq6"}, "DefaultCodeRepository": {}, "AdditionalCodeRepositories": {"shape": "Sq9"}, "RootAccess": {}, "PlatformIdentifier": {}, "InstanceMetadataServiceConfiguration": {"shape": "Sqc"}}}}, "DescribeNotebookInstanceLifecycleConfig": {"input": {"type": "structure", "required": ["NotebookInstanceLifecycleConfigName"], "members": {"NotebookInstanceLifecycleConfigName": {}}}, "output": {"type": "structure", "members": {"NotebookInstanceLifecycleConfigArn": {}, "NotebookInstanceLifecycleConfigName": {}, "OnCreate": {"shape": "Sqh"}, "OnStart": {"shape": "Sqh"}, "LastModifiedTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}}}}, "DescribeOptimizationJob": {"input": {"type": "structure", "required": ["OptimizationJobName"], "members": {"OptimizationJobName": {}}}, "output": {"type": "structure", "required": ["OptimizationJobArn", "OptimizationJobStatus", "CreationTime", "LastModifiedTime", "OptimizationJobName", "ModelSource", "DeploymentInstanceType", "OptimizationConfigs", "OutputConfig", "RoleArn", "StoppingCondition"], "members": {"OptimizationJobArn": {}, "OptimizationJobStatus": {}, "OptimizationStartTime": {"type": "timestamp"}, "OptimizationEndTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}, "OptimizationJobName": {}, "ModelSource": {"shape": "Sqn"}, "OptimizationEnvironment": {"shape": "Sqs"}, "DeploymentInstanceType": {}, "OptimizationConfigs": {"shape": "Sqt"}, "OutputConfig": {"shape": "Sqy"}, "OptimizationOutput": {"type": "structure", "members": {"RecommendedInferenceImage": {}}}, "RoleArn": {}, "StoppingCondition": {"shape": "S3y"}, "VpcConfig": {"shape": "Sqz"}}}}, "DescribePipeline": {"input": {"type": "structure", "required": ["PipelineName"], "members": {"PipelineName": {}}}, "output": {"type": "structure", "members": {"PipelineArn": {}, "PipelineName": {}, "PipelineDisplayName": {}, "PipelineDefinition": {}, "PipelineDescription": {}, "RoleArn": {}, "PipelineStatus": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "LastRunTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedBy": {"shape": "Sz5"}, "ParallelismConfiguration": {"shape": "Srf"}}}}, "DescribePipelineDefinitionForExecution": {"input": {"type": "structure", "required": ["PipelineExecutionArn"], "members": {"PipelineExecutionArn": {}}}, "output": {"type": "structure", "members": {"PipelineDefinition": {}, "CreationTime": {"type": "timestamp"}}}}, "DescribePipelineExecution": {"input": {"type": "structure", "required": ["PipelineExecutionArn"], "members": {"PipelineExecutionArn": {}}}, "output": {"type": "structure", "members": {"PipelineArn": {}, "PipelineExecutionArn": {}, "PipelineExecutionDisplayName": {}, "PipelineExecutionStatus": {}, "PipelineExecutionDescription": {}, "PipelineExperimentConfig": {"shape": "S17x"}, "FailureReason": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedBy": {"shape": "Sz5"}, "ParallelismConfiguration": {"shape": "Srf"}, "SelectiveExecutionConfig": {"shape": "S17z"}}}}, "DescribeProcessingJob": {"input": {"type": "structure", "required": ["ProcessingJobName"], "members": {"ProcessingJobName": {}}}, "output": {"type": "structure", "required": ["ProcessingJobName", "ProcessingResources", "AppSpecification", "ProcessingJobArn", "ProcessingJobStatus", "CreationTime"], "members": {"ProcessingInputs": {"shape": "Srv"}, "ProcessingOutputConfig": {"shape": "Ssi"}, "ProcessingJobName": {}, "ProcessingResources": {"shape": "Ssn"}, "StoppingCondition": {"shape": "Ssp"}, "AppSpecification": {"shape": "Ssr"}, "Environment": {"shape": "Sst"}, "NetworkConfig": {"shape": "Spx"}, "RoleArn": {}, "ExperimentConfig": {"shape": "Ssu"}, "ProcessingJobArn": {}, "ProcessingJobStatus": {}, "ExitMessage": {}, "FailureReason": {}, "ProcessingEndTime": {"type": "timestamp"}, "ProcessingStartTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "MonitoringScheduleArn": {}, "AutoMLJobArn": {}, "TrainingJobArn": {}}}}, "DescribeProject": {"input": {"type": "structure", "required": ["ProjectName"], "members": {"ProjectName": {}}}, "output": {"type": "structure", "required": ["ProjectArn", "ProjectName", "ProjectId", "ServiceCatalogProvisioningDetails", "ProjectStatus", "CreationTime"], "members": {"ProjectArn": {}, "ProjectName": {}, "ProjectId": {}, "ProjectDescription": {}, "ServiceCatalogProvisioningDetails": {"shape": "Ssz"}, "ServiceCatalogProvisionedProductDetails": {"shape": "S188"}, "ProjectStatus": {}, "CreatedBy": {"shape": "Sz5"}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}}}}, "DescribeSpace": {"input": {"type": "structure", "required": ["DomainId", "SpaceName"], "members": {"DomainId": {}, "SpaceName": {}}}, "output": {"type": "structure", "members": {"DomainId": {}, "SpaceArn": {}, "SpaceName": {}, "HomeEfsFileSystemUid": {}, "Status": {}, "LastModifiedTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "FailureReason": {}, "SpaceSettings": {"shape": "St9"}, "OwnershipSettings": {"shape": "Stj"}, "SpaceSharingSettings": {"shape": "Stk"}, "SpaceDisplayName": {}, "Url": {}}}}, "DescribeStudioLifecycleConfig": {"input": {"type": "structure", "required": ["StudioLifecycleConfigName"], "members": {"StudioLifecycleConfigName": {}}}, "output": {"type": "structure", "members": {"StudioLifecycleConfigArn": {}, "StudioLifecycleConfigName": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "StudioLifecycleConfigContent": {}, "StudioLifecycleConfigAppType": {}}}}, "DescribeSubscribedWorkteam": {"input": {"type": "structure", "required": ["WorkteamArn"], "members": {"WorkteamArn": {}}}, "output": {"type": "structure", "required": ["SubscribedWorkteam"], "members": {"SubscribedWorkteam": {"shape": "S18j"}}}}, "DescribeTrainingJob": {"input": {"type": "structure", "required": ["TrainingJobName"], "members": {"TrainingJobName": {}}}, "output": {"type": "structure", "required": ["TrainingJobName", "TrainingJobArn", "ModelArtifacts", "TrainingJobStatus", "SecondaryStatus", "AlgorithmSpecification", "ResourceConfig", "StoppingCondition", "CreationTime"], "members": {"TrainingJobName": {}, "TrainingJobArn": {}, "TuningJobArn": {}, "LabelingJobArn": {}, "AutoMLJobArn": {}, "ModelArtifacts": {"shape": "S11k"}, "TrainingJobStatus": {}, "SecondaryStatus": {}, "FailureReason": {}, "HyperParameters": {"shape": "S35"}, "AlgorithmSpecification": {"shape": "Stv"}, "RoleArn": {}, "InputDataConfig": {"shape": "S37"}, "OutputDataConfig": {"shape": "S3p"}, "ResourceConfig": {"shape": "S3s"}, "WarmPoolStatus": {"shape": "S18n"}, "VpcConfig": {"shape": "S6d"}, "StoppingCondition": {"shape": "S3y"}, "CreationTime": {"type": "timestamp"}, "TrainingStartTime": {"type": "timestamp"}, "TrainingEndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "SecondaryStatusTransitions": {"shape": "S18q"}, "FinalMetricDataList": {"shape": "S18t"}, "EnableNetworkIsolation": {"type": "boolean"}, "EnableInterContainerTrafficEncryption": {"type": "boolean"}, "EnableManagedSpotTraining": {"type": "boolean"}, "CheckpointConfig": {"shape": "Sj3"}, "TrainingTimeInSeconds": {"type": "integer"}, "BillableTimeInSeconds": {"type": "integer"}, "DebugHookConfig": {"shape": "Su4"}, "ExperimentConfig": {"shape": "Ssu"}, "DebugRuleConfigurations": {"shape": "Suc"}, "TensorBoardOutputConfig": {"shape": "<PERSON><PERSON>"}, "DebugRuleEvaluationStatuses": {"shape": "S18x"}, "ProfilerConfig": {"shape": "<PERSON><PERSON>"}, "ProfilerRuleConfigurations": {"shape": "Sul"}, "ProfilerRuleEvaluationStatuses": {"type": "list", "member": {"type": "structure", "members": {"RuleConfigurationName": {}, "RuleEvaluationJobArn": {}, "RuleEvaluationStatus": {}, "StatusDetails": {}, "LastModifiedTime": {"type": "timestamp"}}}}, "ProfilingStatus": {}, "Environment": {"shape": "Sun"}, "RetryStrategy": {"shape": "Sj4"}, "RemoteDebugConfig": {"shape": "Suq"}, "InfraCheckConfig": {"shape": "<PERSON><PERSON>"}}}}, "DescribeTransformJob": {"input": {"type": "structure", "required": ["TransformJobName"], "members": {"TransformJobName": {}}}, "output": {"type": "structure", "required": ["TransformJobName", "TransformJobArn", "TransformJobStatus", "ModelName", "TransformInput", "TransformResources", "CreationTime"], "members": {"TransformJobName": {}, "TransformJobArn": {}, "TransformJobStatus": {}, "FailureReason": {}, "ModelName": {}, "MaxConcurrentTransforms": {"type": "integer"}, "ModelClientConfig": {"shape": "Sv0"}, "MaxPayloadInMB": {"type": "integer"}, "BatchStrategy": {}, "Environment": {"shape": "S46"}, "TransformInput": {"shape": "S49"}, "TransformOutput": {"shape": "S4d"}, "DataCaptureConfig": {"shape": "Sv3"}, "TransformResources": {"shape": "S4g"}, "CreationTime": {"type": "timestamp"}, "TransformStartTime": {"type": "timestamp"}, "TransformEndTime": {"type": "timestamp"}, "LabelingJobArn": {}, "AutoMLJobArn": {}, "DataProcessing": {"shape": "Sv4"}, "ExperimentConfig": {"shape": "Ssu"}}}}, "DescribeTrial": {"input": {"type": "structure", "required": ["TrialName"], "members": {"TrialName": {}}}, "output": {"type": "structure", "members": {"TrialName": {}, "TrialArn": {}, "DisplayName": {}, "ExperimentName": {}, "Source": {"shape": "S199"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "MetadataProperties": {"shape": "S23"}}}}, "DescribeTrialComponent": {"input": {"type": "structure", "required": ["TrialComponentName"], "members": {"TrialComponentName": {}}}, "output": {"type": "structure", "members": {"TrialComponentName": {}, "TrialComponentArn": {}, "DisplayName": {}, "Source": {"shape": "S19d"}, "Status": {"shape": "Svc"}, "StartTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "Parameters": {"shape": "Svf"}, "InputArtifacts": {"shape": "Svj"}, "OutputArtifacts": {"shape": "Svj"}, "MetadataProperties": {"shape": "S23"}, "Metrics": {"shape": "S19f"}, "LineageGroupArn": {}, "Sources": {"type": "list", "member": {"shape": "S19d"}}}}}, "DescribeUserProfile": {"input": {"type": "structure", "required": ["DomainId", "UserProfileName"], "members": {"DomainId": {}, "UserProfileName": {}}}, "output": {"type": "structure", "members": {"DomainId": {}, "UserProfileArn": {}, "UserProfileName": {}, "HomeEfsFileSystemUid": {}, "Status": {}, "LastModifiedTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "FailureReason": {}, "SingleSignOnUserIdentifier": {}, "SingleSignOnUserValue": {}, "UserSettings": {"shape": "Sar"}}}}, "DescribeWorkforce": {"input": {"type": "structure", "required": ["WorkforceName"], "members": {"WorkforceName": {}}}, "output": {"type": "structure", "required": ["Workforce"], "members": {"Workforce": {"shape": "S19p"}}}}, "DescribeWorkteam": {"input": {"type": "structure", "required": ["WorkteamName"], "members": {"WorkteamName": {}}}, "output": {"type": "structure", "required": ["Workteam"], "members": {"Workteam": {"shape": "S19x"}}}}, "DisableSagemakerServicecatalogPortfolio": {"input": {"type": "structure", "members": {}}, "output": {"type": "structure", "members": {}}}, "DisassociateTrialComponent": {"input": {"type": "structure", "required": ["TrialComponentName", "TrialName"], "members": {"TrialComponentName": {}, "TrialName": {}}}, "output": {"type": "structure", "members": {"TrialComponentArn": {}, "TrialArn": {}}}}, "EnableSagemakerServicecatalogPortfolio": {"input": {"type": "structure", "members": {}}, "output": {"type": "structure", "members": {}}}, "GetDeviceFleetReport": {"input": {"type": "structure", "required": ["DeviceFleetName"], "members": {"DeviceFleetName": {}}}, "output": {"type": "structure", "required": ["DeviceFleetArn", "DeviceFleetName"], "members": {"DeviceFleetArn": {}, "DeviceFleetName": {}, "OutputConfig": {"shape": "Sal"}, "Description": {}, "ReportGenerated": {"type": "timestamp"}, "DeviceStats": {"type": "structure", "required": ["ConnectedDeviceCount", "RegisteredDeviceCount"], "members": {"ConnectedDeviceCount": {"type": "long"}, "RegisteredDeviceCount": {"type": "long"}}}, "AgentVersions": {"type": "list", "member": {"type": "structure", "required": ["Version", "AgentCount"], "members": {"Version": {}, "AgentCount": {"type": "long"}}}}, "ModelStats": {"type": "list", "member": {"type": "structure", "required": ["ModelName", "ModelVersion", "OfflineDeviceCount", "ConnectedDeviceCount", "ActiveDeviceCount", "SamplingDeviceCount"], "members": {"ModelName": {}, "ModelVersion": {}, "OfflineDeviceCount": {"type": "long"}, "ConnectedDeviceCount": {"type": "long"}, "ActiveDeviceCount": {"type": "long"}, "SamplingDeviceCount": {"type": "long"}}}}}}}, "GetLineageGroupPolicy": {"input": {"type": "structure", "required": ["LineageGroupName"], "members": {"LineageGroupName": {}}}, "output": {"type": "structure", "members": {"LineageGroupArn": {}, "ResourcePolicy": {}}}}, "GetModelPackageGroupPolicy": {"input": {"type": "structure", "required": ["ModelPackageGroupName"], "members": {"ModelPackageGroupName": {}}}, "output": {"type": "structure", "required": ["ResourcePolicy"], "members": {"ResourcePolicy": {}}}}, "GetSagemakerServicecatalogPortfolioStatus": {"input": {"type": "structure", "members": {}}, "output": {"type": "structure", "members": {"Status": {}}}}, "GetScalingConfigurationRecommendation": {"input": {"type": "structure", "required": ["InferenceRecommendationsJobName"], "members": {"InferenceRecommendationsJobName": {}, "RecommendationId": {}, "EndpointName": {}, "TargetCpuUtilizationPerCore": {"type": "integer"}, "ScalingPolicyObjective": {"shape": "S1ap"}}}, "output": {"type": "structure", "members": {"InferenceRecommendationsJobName": {}, "RecommendationId": {}, "EndpointName": {}, "TargetCpuUtilizationPerCore": {"type": "integer"}, "ScalingPolicyObjective": {"shape": "S1ap"}, "Metric": {"type": "structure", "members": {"InvocationsPerInstance": {"type": "integer"}, "ModelLatency": {"type": "integer"}}}, "DynamicScalingConfiguration": {"type": "structure", "members": {"MinCapacity": {"type": "integer"}, "MaxCapacity": {"type": "integer"}, "ScaleInCooldown": {"type": "integer"}, "ScaleOutCooldown": {"type": "integer"}, "ScalingPolicies": {"type": "list", "member": {"type": "structure", "members": {"TargetTracking": {"type": "structure", "members": {"MetricSpecification": {"type": "structure", "members": {"Predefined": {"type": "structure", "members": {"PredefinedMetricType": {}}}, "Customized": {"type": "structure", "members": {"MetricName": {}, "Namespace": {}, "Statistic": {}}}}, "union": true}, "TargetValue": {"type": "double"}}}}, "union": true}}}}}}}, "GetSearchSuggestions": {"input": {"type": "structure", "required": ["Resource"], "members": {"Resource": {}, "SuggestionQuery": {"type": "structure", "members": {"PropertyNameQuery": {"type": "structure", "required": ["PropertyNameHint"], "members": {"PropertyNameHint": {}}}}}}}, "output": {"type": "structure", "members": {"PropertyNameSuggestions": {"type": "list", "member": {"type": "structure", "members": {"PropertyName": {}}}}}}}, "ImportHubContent": {"input": {"type": "structure", "required": ["HubContentName", "HubContentType", "DocumentSchemaVersion", "<PERSON><PERSON><PERSON><PERSON>", "HubContentDocument"], "members": {"HubContentName": {}, "HubContentVersion": {}, "HubContentType": {}, "DocumentSchemaVersion": {}, "HubName": {}, "HubContentDisplayName": {}, "HubContentDescription": {}, "HubContentMarkdown": {}, "HubContentDocument": {}, "HubContentSearchKeywords": {"shape": "S148"}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "HubContentArn"], "members": {"HubArn": {}, "HubContentArn": {}}}}, "ListActions": {"input": {"type": "structure", "members": {"SourceUri": {}, "ActionType": {}, "CreatedAfter": {"type": "timestamp"}, "CreatedBefore": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"ActionSummaries": {"type": "list", "member": {"type": "structure", "members": {"ActionArn": {}, "ActionName": {}, "Source": {"shape": "S1w"}, "ActionType": {}, "Status": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListAlgorithms": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "MaxResults": {"type": "integer"}, "NameContains": {}, "NextToken": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["AlgorithmSummaryList"], "members": {"AlgorithmSummaryList": {"type": "list", "member": {"type": "structure", "required": ["AlgorithmName", "AlgorithmArn", "CreationTime", "AlgorithmStatus"], "members": {"AlgorithmName": {}, "AlgorithmArn": {}, "AlgorithmDescription": {}, "CreationTime": {"type": "timestamp"}, "AlgorithmStatus": {}}}}, "NextToken": {}}}}, "ListAliases": {"input": {"type": "structure", "required": ["ImageName"], "members": {"ImageName": {}, "Alias": {}, "Version": {"type": "integer"}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"SageMakerImageVersionAliases": {"shape": "Sjp"}, "NextToken": {}}}}, "ListAppImageConfigs": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "ModifiedTimeBefore": {"type": "timestamp"}, "ModifiedTimeAfter": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "members": {"NextToken": {}, "AppImageConfigs": {"type": "list", "member": {"type": "structure", "members": {"AppImageConfigArn": {}, "AppImageConfigName": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "KernelGatewayImageConfig": {"shape": "S51"}, "JupyterLabAppImageConfig": {"shape": "S5a"}, "CodeEditorAppImageConfig": {"shape": "S5h"}}}}}}}, "ListApps": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "SortOrder": {}, "SortBy": {}, "DomainIdEquals": {}, "UserProfileNameEquals": {}, "SpaceNameEquals": {}}}, "output": {"type": "structure", "members": {"Apps": {"type": "list", "member": {"type": "structure", "members": {"DomainId": {}, "UserProfileName": {}, "SpaceName": {}, "AppType": {}, "AppName": {}, "Status": {}, "CreationTime": {"type": "timestamp"}, "ResourceSpec": {"shape": "S4r"}}}}, "NextToken": {}}}}, "ListArtifacts": {"input": {"type": "structure", "members": {"SourceUri": {}, "ArtifactType": {}, "CreatedAfter": {"type": "timestamp"}, "CreatedBefore": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"ArtifactSummaries": {"type": "list", "member": {"type": "structure", "members": {"ArtifactArn": {}, "ArtifactName": {}, "Source": {"shape": "S5l"}, "ArtifactType": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListAssociations": {"input": {"type": "structure", "members": {"SourceArn": {}, "DestinationArn": {}, "SourceType": {}, "DestinationType": {}, "AssociationType": {}, "CreatedAfter": {"type": "timestamp"}, "CreatedBefore": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"AssociationSummaries": {"type": "list", "member": {"type": "structure", "members": {"SourceArn": {}, "DestinationArn": {}, "SourceType": {}, "DestinationType": {}, "AssociationType": {}, "SourceName": {}, "DestinationName": {}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}}}}, "NextToken": {}}}}, "ListAutoMLJobs": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "NameContains": {}, "StatusEquals": {}, "SortOrder": {}, "SortBy": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "required": ["AutoMLJobSummaries"], "members": {"AutoMLJobSummaries": {"type": "list", "member": {"type": "structure", "required": ["AutoMLJobName", "AutoMLJobArn", "AutoMLJobStatus", "AutoMLJobSecondaryStatus", "CreationTime", "LastModifiedTime"], "members": {"AutoMLJobName": {}, "AutoMLJobArn": {}, "AutoMLJobStatus": {}, "AutoMLJobSecondaryStatus": {}, "CreationTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}, "PartialFailureReasons": {"shape": "Szq"}}}}, "NextToken": {}}}}, "ListCandidatesForAutoMLJob": {"input": {"type": "structure", "required": ["AutoMLJobName"], "members": {"AutoMLJobName": {}, "StatusEquals": {}, "CandidateNameEquals": {}, "SortOrder": {}, "SortBy": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "required": ["Candidates"], "members": {"Candidates": {"type": "list", "member": {"shape": "Szs"}}, "NextToken": {}}}}, "ListClusterNodes": {"input": {"type": "structure", "required": ["ClusterName"], "members": {"ClusterName": {}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "InstanceGroupNameContains": {}, "MaxResults": {"type": "integer"}, "NextToken": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["NextToken", "ClusterNodeSummaries"], "members": {"NextToken": {}, "ClusterNodeSummaries": {"type": "list", "member": {"type": "structure", "required": ["InstanceGroupName", "InstanceId", "InstanceType", "LaunchTime", "InstanceStatus"], "members": {"InstanceGroupName": {}, "InstanceId": {}, "InstanceType": {}, "LaunchTime": {"type": "timestamp"}, "InstanceStatus": {"shape": "S116"}}}}}}}, "ListClusters": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "MaxResults": {"type": "integer"}, "NameContains": {}, "NextToken": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["NextToken", "ClusterSummaries"], "members": {"NextToken": {}, "ClusterSummaries": {"type": "list", "member": {"type": "structure", "required": ["ClusterArn", "ClusterName", "CreationTime", "ClusterStatus"], "members": {"ClusterArn": {}, "ClusterName": {}, "CreationTime": {"type": "timestamp"}, "ClusterStatus": {}}}}}}}, "ListCodeRepositories": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "MaxResults": {"type": "integer"}, "NameContains": {}, "NextToken": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["CodeRepositorySummaryList"], "members": {"CodeRepositorySummaryList": {"type": "list", "member": {"type": "structure", "required": ["CodeRepositoryName", "CodeRepositoryArn", "CreationTime", "LastModifiedTime"], "members": {"CodeRepositoryName": {}, "CodeRepositoryArn": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "GitConfig": {"shape": "S8h"}}}}, "NextToken": {}}}}, "ListCompilationJobs": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "NameContains": {}, "StatusEquals": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["CompilationJobSummaries"], "members": {"CompilationJobSummaries": {"type": "list", "member": {"type": "structure", "required": ["CompilationJobName", "CompilationJobArn", "CreationTime", "CompilationJobStatus"], "members": {"CompilationJobName": {}, "CompilationJobArn": {}, "CreationTime": {"type": "timestamp"}, "CompilationStartTime": {"type": "timestamp"}, "CompilationEndTime": {"type": "timestamp"}, "CompilationTargetDevice": {}, "CompilationTargetPlatformOs": {}, "CompilationTargetPlatformArch": {}, "CompilationTargetPlatformAccelerator": {}, "LastModifiedTime": {"type": "timestamp"}, "CompilationJobStatus": {}}}}, "NextToken": {}}}}, "ListContexts": {"input": {"type": "structure", "members": {"SourceUri": {}, "ContextType": {}, "CreatedAfter": {"type": "timestamp"}, "CreatedBefore": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"ContextSummaries": {"type": "list", "member": {"type": "structure", "members": {"ContextArn": {}, "ContextName": {}, "Source": {"shape": "S97"}, "ContextType": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListDataQualityJobDefinitions": {"input": {"type": "structure", "members": {"EndpointName": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}}}, "output": {"type": "structure", "required": ["JobDefinitionSummaries"], "members": {"JobDefinitionSummaries": {"shape": "S1dh"}, "NextToken": {}}}}, "ListDeviceFleets": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "NameContains": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["DeviceFleetSummaries"], "members": {"DeviceFleetSummaries": {"type": "list", "member": {"type": "structure", "required": ["DeviceFleetArn", "DeviceFleetName"], "members": {"DeviceFleetArn": {}, "DeviceFleetName": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListDevices": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "LatestHeartbeatAfter": {"type": "timestamp"}, "ModelName": {}, "DeviceFleetName": {}}}, "output": {"type": "structure", "required": ["DeviceSummaries"], "members": {"DeviceSummaries": {"type": "list", "member": {"type": "structure", "required": ["DeviceName", "DeviceArn"], "members": {"DeviceName": {}, "DeviceArn": {}, "Description": {}, "DeviceFleetName": {}, "IotThingName": {}, "RegistrationTime": {"type": "timestamp"}, "LatestHeartbeat": {"type": "timestamp"}, "Models": {"type": "list", "member": {"type": "structure", "required": ["ModelName", "ModelVersion"], "members": {"ModelName": {}, "ModelVersion": {}}}}, "AgentVersion": {}}}}, "NextToken": {}}}}, "ListDomains": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Domains": {"type": "list", "member": {"type": "structure", "members": {"DomainArn": {}, "DomainId": {}, "DomainName": {}, "Status": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "Url": {}}}}, "NextToken": {}}}}, "ListEdgeDeploymentPlans": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "NameContains": {}, "DeviceFleetNameContains": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["EdgeDeploymentPlanSummaries"], "members": {"EdgeDeploymentPlanSummaries": {"type": "list", "member": {"type": "structure", "required": ["EdgeDeploymentPlanArn", "EdgeDeploymentPlanName", "DeviceFleetName", "EdgeDeploymentSuccess", "EdgeDeploymentPending", "EdgeDeploymentFailed"], "members": {"EdgeDeploymentPlanArn": {}, "EdgeDeploymentPlanName": {}, "DeviceFleetName": {}, "EdgeDeploymentSuccess": {"type": "integer"}, "EdgeDeploymentPending": {"type": "integer"}, "EdgeDeploymentFailed": {"type": "integer"}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListEdgePackagingJobs": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "NameContains": {}, "ModelNameContains": {}, "StatusEquals": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["EdgePackagingJobSummaries"], "members": {"EdgePackagingJobSummaries": {"type": "list", "member": {"type": "structure", "required": ["EdgePackagingJobArn", "EdgePackagingJobName", "EdgePackagingJobStatus"], "members": {"EdgePackagingJobArn": {}, "EdgePackagingJobName": {}, "EdgePackagingJobStatus": {}, "CompilationJobName": {}, "ModelName": {}, "ModelVersion": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListEndpointConfigs": {"input": {"type": "structure", "members": {"SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}}}, "output": {"type": "structure", "required": ["EndpointConfigs"], "members": {"EndpointConfigs": {"type": "list", "member": {"type": "structure", "required": ["EndpointConfigName", "EndpointConfigArn", "CreationTime"], "members": {"EndpointConfigName": {}, "EndpointConfigArn": {}, "CreationTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListEndpoints": {"input": {"type": "structure", "members": {"SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "StatusEquals": {}}}, "output": {"type": "structure", "required": ["Endpoints"], "members": {"Endpoints": {"type": "list", "member": {"type": "structure", "required": ["EndpointName", "EndpointArn", "CreationTime", "LastModifiedTime", "EndpointStatus"], "members": {"EndpointName": {}, "EndpointArn": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "EndpointStatus": {}}}}, "NextToken": {}}}}, "ListExperiments": {"input": {"type": "structure", "members": {"CreatedAfter": {"type": "timestamp"}, "CreatedBefore": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"ExperimentSummaries": {"type": "list", "member": {"type": "structure", "members": {"ExperimentArn": {}, "ExperimentName": {}, "DisplayName": {}, "ExperimentSource": {"shape": "S138"}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListFeatureGroups": {"input": {"type": "structure", "members": {"NameContains": {}, "FeatureGroupStatusEquals": {}, "OfflineStoreStatusEquals": {}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "SortOrder": {}, "SortBy": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "required": ["FeatureGroupSummaries"], "members": {"FeatureGroupSummaries": {"type": "list", "member": {"type": "structure", "required": ["FeatureGroupName", "FeatureGroupArn", "CreationTime"], "members": {"FeatureGroupName": {}, "FeatureGroupArn": {}, "CreationTime": {"type": "timestamp"}, "FeatureGroupStatus": {}, "OfflineStoreStatus": {"shape": "S13g"}}}}, "NextToken": {}}}}, "ListFlowDefinitions": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["FlowDefinitionSummaries"], "members": {"FlowDefinitionSummaries": {"type": "list", "member": {"type": "structure", "required": ["FlowDefinitionName", "FlowDefinitionArn", "FlowDefinitionStatus", "CreationTime"], "members": {"FlowDefinitionName": {}, "FlowDefinitionArn": {}, "FlowDefinitionStatus": {}, "CreationTime": {"type": "timestamp"}, "FailureReason": {}}}}, "NextToken": {}}}}, "ListHubContentVersions": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "HubContentType", "HubContentName"], "members": {"HubName": {}, "HubContentType": {}, "HubContentName": {}, "MinVersion": {}, "MaxSchemaVersion": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "required": ["HubContentSummaries"], "members": {"HubContentSummaries": {"shape": "S1f7"}, "NextToken": {}}}}, "ListHubContents": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "HubContentType"], "members": {"HubName": {}, "HubContentType": {}, "NameContains": {}, "MaxSchemaVersion": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "required": ["HubContentSummaries"], "members": {"HubContentSummaries": {"shape": "S1f7"}, "NextToken": {}}}}, "ListHubs": {"input": {"type": "structure", "members": {"NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "required": ["HubSummaries"], "members": {"HubSummaries": {"type": "list", "member": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>b<PERSON><PERSON><PERSON>", "CreationTime", "LastModifiedTime"], "members": {"HubName": {}, "HubArn": {}, "HubDisplayName": {}, "HubDescription": {}, "HubSearchKeywords": {"shape": "Shh"}, "HubStatus": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListHumanTaskUis": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["HumanTaskUiSummaries"], "members": {"HumanTaskUiSummaries": {"type": "list", "member": {"type": "structure", "required": ["HumanTaskUiName", "HumanTaskUiArn", "CreationTime"], "members": {"HumanTaskUiName": {}, "HumanTaskUiArn": {}, "CreationTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListHyperParameterTuningJobs": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "SortBy": {}, "SortOrder": {}, "NameContains": {}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "StatusEquals": {}}}, "output": {"type": "structure", "required": ["HyperParameterTuningJobSummaries"], "members": {"HyperParameterTuningJobSummaries": {"type": "list", "member": {"type": "structure", "required": ["HyperParameterTuningJobName", "HyperParameterTuningJobArn", "HyperParameterTuningJobStatus", "Strategy", "CreationTime", "TrainingJobStatusCounters", "ObjectiveStatusCounters"], "members": {"HyperParameterTuningJobName": {}, "HyperParameterTuningJobArn": {}, "HyperParameterTuningJobStatus": {}, "Strategy": {}, "CreationTime": {"type": "timestamp"}, "HyperParameterTuningEndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "TrainingJobStatusCounters": {"shape": "S14n"}, "ObjectiveStatusCounters": {"shape": "S14p"}, "ResourceLimits": {"shape": "Si6"}}}}, "NextToken": {}}}}, "ListImageVersions": {"input": {"type": "structure", "required": ["ImageName"], "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "ImageName": {}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "MaxResults": {"type": "integer"}, "NextToken": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "members": {"ImageVersions": {"type": "list", "member": {"type": "structure", "required": ["CreationTime", "ImageArn", "ImageVersionArn", "ImageVersionStatus", "LastModifiedTime", "Version"], "members": {"CreationTime": {"type": "timestamp"}, "FailureReason": {}, "ImageArn": {}, "ImageVersionArn": {}, "ImageVersionStatus": {}, "LastModifiedTime": {"type": "timestamp"}, "Version": {"type": "integer"}}}}, "NextToken": {}}}}, "ListImages": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "MaxResults": {"type": "integer"}, "NameContains": {}, "NextToken": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "members": {"Images": {"type": "list", "member": {"type": "structure", "required": ["CreationTime", "ImageArn", "ImageName", "ImageStatus", "LastModifiedTime"], "members": {"CreationTime": {"type": "timestamp"}, "Description": {}, "DisplayName": {}, "FailureReason": {}, "ImageArn": {}, "ImageName": {}, "ImageStatus": {}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListInferenceComponents": {"input": {"type": "structure", "members": {"SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "StatusEquals": {}, "EndpointNameEquals": {}, "VariantNameEquals": {}}}, "output": {"type": "structure", "required": ["InferenceComponents"], "members": {"InferenceComponents": {"type": "list", "member": {"type": "structure", "required": ["CreationTime", "InferenceComponentArn", "InferenceComponentName", "EndpointArn", "EndpointName", "VariantName", "LastModifiedTime"], "members": {"CreationTime": {"type": "timestamp"}, "InferenceComponentArn": {}, "InferenceComponentName": {}, "EndpointArn": {}, "EndpointName": {}, "VariantName": {}, "InferenceComponentStatus": {}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListInferenceExperiments": {"input": {"type": "structure", "members": {"NameContains": {}, "Type": {}, "StatusEquals": {}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"InferenceExperiments": {"type": "list", "member": {"type": "structure", "required": ["Name", "Type", "Status", "CreationTime", "LastModifiedTime"], "members": {"Name": {}, "Type": {}, "Schedule": {"shape": "Skf"}, "Status": {}, "StatusReason": {}, "Description": {}, "CreationTime": {"type": "timestamp"}, "CompletionTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "RoleArn": {}}}}, "NextToken": {}}}}, "ListInferenceRecommendationsJobSteps": {"input": {"type": "structure", "required": ["JobName"], "members": {"JobName": {}, "Status": {}, "StepType": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Steps": {"type": "list", "member": {"type": "structure", "required": ["StepType", "JobName", "Status"], "members": {"StepType": {}, "JobName": {}, "Status": {}, "InferenceBenchmark": {"type": "structure", "required": ["ModelConfiguration"], "members": {"Metrics": {"shape": "S15m"}, "EndpointMetrics": {"shape": "S15z"}, "EndpointConfiguration": {"shape": "S15p"}, "ModelConfiguration": {"shape": "S15r"}, "FailureReason": {}, "InvocationEndTime": {"type": "timestamp"}, "InvocationStartTime": {"type": "timestamp"}}}}}}, "NextToken": {}}}}, "ListInferenceRecommendationsJobs": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "NameContains": {}, "StatusEquals": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "ModelNameEquals": {}, "ModelPackageVersionArnEquals": {}}}, "output": {"type": "structure", "required": ["InferenceRecommendationsJobs"], "members": {"InferenceRecommendationsJobs": {"type": "list", "member": {"type": "structure", "required": ["JobName", "JobDescription", "JobType", "JobArn", "Status", "CreationTime", "RoleArn", "LastModifiedTime"], "members": {"JobName": {}, "JobDescription": {}, "JobType": {}, "JobArn": {}, "Status": {}, "CreationTime": {"type": "timestamp"}, "CompletionTime": {"type": "timestamp"}, "RoleArn": {}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}, "ModelName": {}, "SamplePayloadUrl": {}, "ModelPackageVersionArn": {}}}}, "NextToken": {}}}}, "ListLabelingJobs": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "MaxResults": {"type": "integer"}, "NextToken": {}, "NameContains": {}, "SortBy": {}, "SortOrder": {}, "StatusEquals": {}}}, "output": {"type": "structure", "members": {"LabelingJobSummaryList": {"type": "list", "member": {"type": "structure", "required": ["LabelingJobName", "LabelingJobArn", "CreationTime", "LastModifiedTime", "LabelingJobStatus", "LabelCounters", "WorkteamArn", "PreHumanTaskLambdaArn"], "members": {"LabelingJobName": {}, "LabelingJobArn": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "LabelingJobStatus": {}, "LabelCounters": {"shape": "S163"}, "WorkteamArn": {}, "PreHumanTaskLambdaArn": {}, "AnnotationConsolidationLambdaArn": {}, "FailureReason": {}, "LabelingJobOutput": {"shape": "S166"}, "InputConfig": {"shape": "Smh"}}}}, "NextToken": {}}}}, "ListLabelingJobsForWorkteam": {"input": {"type": "structure", "required": ["WorkteamArn"], "members": {"WorkteamArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "JobReferenceCodeContains": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["LabelingJobSummaryList"], "members": {"LabelingJobSummaryList": {"type": "list", "member": {"type": "structure", "required": ["JobReferenceCode", "WorkRequesterAccountId", "CreationTime"], "members": {"LabelingJobName": {}, "JobReferenceCode": {}, "WorkRequesterAccountId": {}, "CreationTime": {"type": "timestamp"}, "LabelCounters": {"type": "structure", "members": {"HumanLabeled": {"type": "integer"}, "PendingHuman": {"type": "integer"}, "Total": {"type": "integer"}}}, "NumberOfHumanWorkersPerDataObject": {"type": "integer"}}}}, "NextToken": {}}}}, "ListLineageGroups": {"input": {"type": "structure", "members": {"CreatedAfter": {"type": "timestamp"}, "CreatedBefore": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"LineageGroupSummaries": {"type": "list", "member": {"type": "structure", "members": {"LineageGroupArn": {}, "LineageGroupName": {}, "DisplayName": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListMlflowTrackingServers": {"input": {"type": "structure", "members": {"CreatedAfter": {"type": "timestamp"}, "CreatedBefore": {"type": "timestamp"}, "TrackingServerStatus": {}, "MlflowVersion": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"TrackingServerSummaries": {"type": "list", "member": {"type": "structure", "members": {"TrackingServerArn": {}, "TrackingServerName": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "TrackingServerStatus": {}, "IsActive": {}, "MlflowVersion": {}}}}, "NextToken": {}}}}, "ListModelBiasJobDefinitions": {"input": {"type": "structure", "members": {"EndpointName": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}}}, "output": {"type": "structure", "required": ["JobDefinitionSummaries"], "members": {"JobDefinitionSummaries": {"shape": "S1dh"}, "NextToken": {}}}}, "ListModelCardExportJobs": {"input": {"type": "structure", "required": ["ModelCardName"], "members": {"ModelCardName": {}, "ModelCardVersion": {"type": "integer"}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "ModelCardExportJobNameContains": {}, "StatusEquals": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["ModelCardExportJobSummaries"], "members": {"ModelCardExportJobSummaries": {"type": "list", "member": {"type": "structure", "required": ["ModelCardExportJobName", "ModelCardExportJobArn", "Status", "ModelCardName", "ModelCardVersion", "CreatedAt", "LastModifiedAt"], "members": {"ModelCardExportJobName": {}, "ModelCardExportJobArn": {}, "Status": {}, "ModelCardName": {}, "ModelCardVersion": {"type": "integer"}, "CreatedAt": {"type": "timestamp"}, "LastModifiedAt": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListModelCardVersions": {"input": {"type": "structure", "required": ["ModelCardName"], "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "MaxResults": {"type": "integer"}, "ModelCardName": {}, "ModelCardStatus": {}, "NextToken": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["ModelCardVersionSummaryList"], "members": {"ModelCardVersionSummaryList": {"type": "list", "member": {"type": "structure", "required": ["ModelCardName", "ModelCardArn", "ModelCardStatus", "ModelCardVersion", "CreationTime"], "members": {"ModelCardName": {}, "ModelCardArn": {}, "ModelCardStatus": {}, "ModelCardVersion": {"type": "integer"}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListModelCards": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "MaxResults": {"type": "integer"}, "NameContains": {}, "ModelCardStatus": {}, "NextToken": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["ModelCardSummaries"], "members": {"ModelCardSummaries": {"type": "list", "member": {"type": "structure", "required": ["ModelCardName", "ModelCardArn", "ModelCardStatus", "CreationTime"], "members": {"ModelCardName": {}, "ModelCardArn": {}, "ModelCardStatus": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListModelExplainabilityJobDefinitions": {"input": {"type": "structure", "members": {"EndpointName": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}}}, "output": {"type": "structure", "required": ["JobDefinitionSummaries"], "members": {"JobDefinitionSummaries": {"shape": "S1dh"}, "NextToken": {}}}}, "ListModelMetadata": {"input": {"type": "structure", "members": {"SearchExpression": {"type": "structure", "members": {"Filters": {"type": "list", "member": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {}, "Value": {}}}}}}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["ModelMetadataSummaries"], "members": {"ModelMetadataSummaries": {"type": "list", "member": {"type": "structure", "required": ["Domain", "Framework", "Task", "Model", "FrameworkVersion"], "members": {"Domain": {}, "Framework": {}, "Task": {}, "Model": {}, "FrameworkVersion": {}}}}, "NextToken": {}}}}, "ListModelPackageGroups": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "MaxResults": {"type": "integer"}, "NameContains": {}, "NextToken": {}, "SortBy": {}, "SortOrder": {}, "CrossAccountFilterOption": {}}}, "output": {"type": "structure", "required": ["ModelPackageGroupSummaryList"], "members": {"ModelPackageGroupSummaryList": {"type": "list", "member": {"type": "structure", "required": ["ModelPackageGroupName", "ModelPackageGroupArn", "CreationTime", "ModelPackageGroupStatus"], "members": {"ModelPackageGroupName": {}, "ModelPackageGroupArn": {}, "ModelPackageGroupDescription": {}, "CreationTime": {"type": "timestamp"}, "ModelPackageGroupStatus": {}}}}, "NextToken": {}}}}, "ListModelPackages": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "MaxResults": {"type": "integer"}, "NameContains": {}, "ModelApprovalStatus": {}, "ModelPackageGroupName": {}, "ModelPackageType": {}, "NextToken": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["ModelPackageSummaryList"], "members": {"ModelPackageSummaryList": {"type": "list", "member": {"type": "structure", "required": ["ModelPackageArn", "CreationTime", "ModelPackageStatus"], "members": {"ModelPackageName": {}, "ModelPackageGroupName": {}, "ModelPackageVersion": {"type": "integer"}, "ModelPackageArn": {}, "ModelPackageDescription": {}, "CreationTime": {"type": "timestamp"}, "ModelPackageStatus": {}, "ModelApprovalStatus": {}}}}, "NextToken": {}}}}, "ListModelQualityJobDefinitions": {"input": {"type": "structure", "members": {"EndpointName": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}}}, "output": {"type": "structure", "required": ["JobDefinitionSummaries"], "members": {"JobDefinitionSummaries": {"shape": "S1dh"}, "NextToken": {}}}}, "ListModels": {"input": {"type": "structure", "members": {"SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}}}, "output": {"type": "structure", "required": ["Models"], "members": {"Models": {"type": "list", "member": {"type": "structure", "required": ["ModelName", "ModelArn", "CreationTime"], "members": {"ModelName": {}, "ModelArn": {}, "CreationTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListMonitoringAlertHistory": {"input": {"type": "structure", "members": {"MonitoringScheduleName": {}, "MonitoringAlertName": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "StatusEquals": {}}}, "output": {"type": "structure", "members": {"MonitoringAlertHistory": {"type": "list", "member": {"type": "structure", "required": ["MonitoringScheduleName", "MonitoringAlertName", "CreationTime", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"MonitoringScheduleName": {}, "MonitoringAlertName": {}, "CreationTime": {"type": "timestamp"}, "AlertStatus": {}}}}, "NextToken": {}}}}, "ListMonitoringAlerts": {"input": {"type": "structure", "required": ["MonitoringScheduleName"], "members": {"MonitoringScheduleName": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"MonitoringAlertSummaries": {"shape": "S1ix"}, "NextToken": {}}}}, "ListMonitoringExecutions": {"input": {"type": "structure", "members": {"MonitoringScheduleName": {}, "EndpointName": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "ScheduledTimeBefore": {"type": "timestamp"}, "ScheduledTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "StatusEquals": {}, "MonitoringJobDefinitionName": {}, "MonitoringTypeEquals": {}}}, "output": {"type": "structure", "required": ["MonitoringExecutionSummaries"], "members": {"MonitoringExecutionSummaries": {"type": "list", "member": {"shape": "S179"}}, "NextToken": {}}}}, "ListMonitoringSchedules": {"input": {"type": "structure", "members": {"EndpointName": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "StatusEquals": {}, "MonitoringJobDefinitionName": {}, "MonitoringTypeEquals": {}}}, "output": {"type": "structure", "required": ["MonitoringScheduleSummaries"], "members": {"MonitoringScheduleSummaries": {"type": "list", "member": {"type": "structure", "required": ["MonitoringScheduleName", "MonitoringScheduleArn", "CreationTime", "LastModifiedTime", "MonitoringScheduleStatus"], "members": {"MonitoringScheduleName": {}, "MonitoringScheduleArn": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "MonitoringScheduleStatus": {}, "EndpointName": {}, "MonitoringJobDefinitionName": {}, "MonitoringType": {}}}}, "NextToken": {}}}}, "ListNotebookInstanceLifecycleConfigs": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "SortBy": {}, "SortOrder": {}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}}}, "output": {"type": "structure", "members": {"NextToken": {}, "NotebookInstanceLifecycleConfigs": {"type": "list", "member": {"type": "structure", "required": ["NotebookInstanceLifecycleConfigName", "NotebookInstanceLifecycleConfigArn"], "members": {"NotebookInstanceLifecycleConfigName": {}, "NotebookInstanceLifecycleConfigArn": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}}}}, "ListNotebookInstances": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "SortBy": {}, "SortOrder": {}, "NameContains": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "StatusEquals": {}, "NotebookInstanceLifecycleConfigNameContains": {}, "DefaultCodeRepositoryContains": {}, "AdditionalCodeRepositoryEquals": {}}}, "output": {"type": "structure", "members": {"NextToken": {}, "NotebookInstances": {"type": "list", "member": {"type": "structure", "required": ["NotebookInstanceName", "NotebookInstanceArn"], "members": {"NotebookInstanceName": {}, "NotebookInstanceArn": {}, "NotebookInstanceStatus": {}, "Url": {}, "InstanceType": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "NotebookInstanceLifecycleConfigName": {}, "DefaultCodeRepository": {}, "AdditionalCodeRepositories": {"shape": "Sq9"}}}}}}}, "ListOptimizationJobs": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "OptimizationContains": {}, "NameContains": {}, "StatusEquals": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["OptimizationJobSummaries"], "members": {"OptimizationJobSummaries": {"type": "list", "member": {"type": "structure", "required": ["OptimizationJobName", "OptimizationJobArn", "CreationTime", "OptimizationJobStatus", "DeploymentInstanceType", "OptimizationTypes"], "members": {"OptimizationJobName": {}, "OptimizationJobArn": {}, "CreationTime": {"type": "timestamp"}, "OptimizationJobStatus": {}, "OptimizationStartTime": {"type": "timestamp"}, "OptimizationEndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "DeploymentInstanceType": {}, "OptimizationTypes": {"type": "list", "member": {}}}}}, "NextToken": {}}}}, "ListPipelineExecutionSteps": {"input": {"type": "structure", "members": {"PipelineExecutionArn": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "SortOrder": {}}}, "output": {"type": "structure", "members": {"PipelineExecutionSteps": {"type": "list", "member": {"type": "structure", "members": {"StepName": {}, "StepDisplayName": {}, "StepDescription": {}, "StartTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "StepStatus": {}, "CacheHitResult": {"type": "structure", "members": {"SourcePipelineExecutionArn": {}}}, "FailureReason": {}, "Metadata": {"type": "structure", "members": {"TrainingJob": {"type": "structure", "members": {"Arn": {}}}, "ProcessingJob": {"type": "structure", "members": {"Arn": {}}}, "TransformJob": {"type": "structure", "members": {"Arn": {}}}, "TuningJob": {"type": "structure", "members": {"Arn": {}}}, "Model": {"type": "structure", "members": {"Arn": {}}}, "RegisterModel": {"type": "structure", "members": {"Arn": {}}}, "Condition": {"type": "structure", "members": {"Outcome": {}}}, "Callback": {"type": "structure", "members": {"CallbackToken": {}, "SqsQueueUrl": {}, "OutputParameters": {"shape": "S1ki"}}}, "Lambda": {"type": "structure", "members": {"Arn": {}, "OutputParameters": {"shape": "S1ki"}}}, "EMR": {"type": "structure", "members": {"ClusterId": {}, "StepId": {}, "StepName": {}, "LogFilePath": {}}}, "QualityCheck": {"type": "structure", "members": {"CheckType": {}, "BaselineUsedForDriftCheckStatistics": {}, "BaselineUsedForDriftCheckConstraints": {}, "CalculatedBaselineStatistics": {}, "CalculatedBaselineConstraints": {}, "ModelPackageGroupName": {}, "ViolationReport": {}, "CheckJobArn": {}, "SkipCheck": {"type": "boolean"}, "RegisterNewBaseline": {"type": "boolean"}}}, "ClarifyCheck": {"type": "structure", "members": {"CheckType": {}, "BaselineUsedForDriftCheckConstraints": {}, "CalculatedBaselineConstraints": {}, "ModelPackageGroupName": {}, "ViolationReport": {}, "CheckJobArn": {}, "SkipCheck": {"type": "boolean"}, "RegisterNewBaseline": {"type": "boolean"}}}, "Fail": {"type": "structure", "members": {"ErrorMessage": {}}}, "AutoMLJob": {"type": "structure", "members": {"Arn": {}}}, "Endpoint": {"type": "structure", "members": {"Arn": {}}}, "EndpointConfig": {"type": "structure", "members": {"Arn": {}}}}}, "AttemptCount": {"type": "integer"}, "SelectiveExecutionResult": {"type": "structure", "members": {"SourcePipelineExecutionArn": {}}}}}}, "NextToken": {}}}}, "ListPipelineExecutions": {"input": {"type": "structure", "required": ["PipelineName"], "members": {"PipelineName": {}, "CreatedAfter": {"type": "timestamp"}, "CreatedBefore": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"PipelineExecutionSummaries": {"type": "list", "member": {"type": "structure", "members": {"PipelineExecutionArn": {}, "StartTime": {"type": "timestamp"}, "PipelineExecutionStatus": {}, "PipelineExecutionDescription": {}, "PipelineExecutionDisplayName": {}, "PipelineExecutionFailureReason": {}}}}, "NextToken": {}}}}, "ListPipelineParametersForExecution": {"input": {"type": "structure", "required": ["PipelineExecutionArn"], "members": {"PipelineExecutionArn": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"PipelineParameters": {"shape": "S1l1"}, "NextToken": {}}}}, "ListPipelines": {"input": {"type": "structure", "members": {"PipelineNamePrefix": {}, "CreatedAfter": {"type": "timestamp"}, "CreatedBefore": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"PipelineSummaries": {"type": "list", "member": {"type": "structure", "members": {"PipelineArn": {}, "PipelineName": {}, "PipelineDisplayName": {}, "PipelineDescription": {}, "RoleArn": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "LastExecutionTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListProcessingJobs": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "NameContains": {}, "StatusEquals": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["ProcessingJobSummaries"], "members": {"ProcessingJobSummaries": {"type": "list", "member": {"type": "structure", "required": ["ProcessingJobName", "ProcessingJobArn", "CreationTime", "ProcessingJobStatus"], "members": {"ProcessingJobName": {}, "ProcessingJobArn": {}, "CreationTime": {"type": "timestamp"}, "ProcessingEndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "ProcessingJobStatus": {}, "FailureReason": {}, "ExitMessage": {}}}}, "NextToken": {}}}}, "ListProjects": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "MaxResults": {"type": "integer"}, "NameContains": {}, "NextToken": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["ProjectSummaryList"], "members": {"ProjectSummaryList": {"type": "list", "member": {"type": "structure", "required": ["ProjectName", "ProjectArn", "ProjectId", "CreationTime", "ProjectStatus"], "members": {"ProjectName": {}, "ProjectDescription": {}, "ProjectArn": {}, "ProjectId": {}, "CreationTime": {"type": "timestamp"}, "ProjectStatus": {}}}}, "NextToken": {}}}}, "ListResourceCatalogs": {"input": {"type": "structure", "members": {"NameContains": {}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "SortOrder": {}, "SortBy": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"ResourceCatalogs": {"type": "list", "member": {"type": "structure", "required": ["ResourceCatalogArn", "ResourceCatalogName", "Description", "CreationTime"], "members": {"ResourceCatalogArn": {}, "ResourceCatalogName": {}, "Description": {}, "CreationTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListSpaces": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "SortOrder": {}, "SortBy": {}, "DomainIdEquals": {}, "SpaceNameContains": {}}}, "output": {"type": "structure", "members": {"Spaces": {"type": "list", "member": {"type": "structure", "members": {"DomainId": {}, "SpaceName": {}, "Status": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "SpaceSettingsSummary": {"type": "structure", "members": {"AppType": {}, "SpaceStorageSettings": {"shape": "Ste"}}}, "SpaceSharingSettingsSummary": {"type": "structure", "members": {"SharingType": {}}}, "OwnershipSettingsSummary": {"type": "structure", "members": {"OwnerUserProfileName": {}}}, "SpaceDisplayName": {}}}}, "NextToken": {}}}}, "ListStageDevices": {"input": {"type": "structure", "required": ["EdgeDeploymentPlanName", "StageName"], "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "EdgeDeploymentPlanName": {}, "ExcludeDevicesDeployedInOtherStage": {"type": "boolean"}, "StageName": {}}}, "output": {"type": "structure", "required": ["DeviceDeploymentSummaries"], "members": {"DeviceDeploymentSummaries": {"type": "list", "member": {"type": "structure", "required": ["EdgeDeploymentPlanArn", "EdgeDeploymentPlanName", "StageName", "DeviceName", "DeviceArn"], "members": {"EdgeDeploymentPlanArn": {}, "EdgeDeploymentPlanName": {}, "StageName": {}, "DeployedStageName": {}, "DeviceFleetName": {}, "DeviceName": {}, "DeviceArn": {}, "DeviceDeploymentStatus": {}, "DeviceDeploymentStatusMessage": {}, "Description": {}, "DeploymentStartTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListStudioLifecycleConfigs": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}, "NameContains": {}, "AppTypeEquals": {}, "CreationTimeBefore": {"type": "timestamp"}, "CreationTimeAfter": {"type": "timestamp"}, "ModifiedTimeBefore": {"type": "timestamp"}, "ModifiedTimeAfter": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "members": {"NextToken": {}, "StudioLifecycleConfigs": {"type": "list", "member": {"type": "structure", "members": {"StudioLifecycleConfigArn": {}, "StudioLifecycleConfigName": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "StudioLifecycleConfigAppType": {}}}}}}}, "ListSubscribedWorkteams": {"input": {"type": "structure", "members": {"NameContains": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["SubscribedWorkteams"], "members": {"SubscribedWorkteams": {"type": "list", "member": {"shape": "S18j"}}, "NextToken": {}}}}, "ListTags": {"input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "S7"}, "NextToken": {}}}}, "ListTrainingJobs": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "NameContains": {}, "StatusEquals": {}, "SortBy": {}, "SortOrder": {}, "WarmPoolStatusEquals": {}}}, "output": {"type": "structure", "required": ["TrainingJobSummaries"], "members": {"TrainingJobSummaries": {"type": "list", "member": {"type": "structure", "required": ["TrainingJobName", "TrainingJobArn", "CreationTime", "TrainingJobStatus"], "members": {"TrainingJobName": {}, "TrainingJobArn": {}, "CreationTime": {"type": "timestamp"}, "TrainingEndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "TrainingJobStatus": {}, "WarmPoolStatus": {"shape": "S18n"}}}}, "NextToken": {}}}}, "ListTrainingJobsForHyperParameterTuningJob": {"input": {"type": "structure", "required": ["HyperParameterTuningJobName"], "members": {"HyperParameterTuningJobName": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "StatusEquals": {}, "SortBy": {}, "SortOrder": {}}}, "output": {"type": "structure", "required": ["TrainingJobSummaries"], "members": {"TrainingJobSummaries": {"type": "list", "member": {"shape": "S14r"}}, "NextToken": {}}}}, "ListTransformJobs": {"input": {"type": "structure", "members": {"CreationTimeAfter": {"type": "timestamp"}, "CreationTimeBefore": {"type": "timestamp"}, "LastModifiedTimeAfter": {"type": "timestamp"}, "LastModifiedTimeBefore": {"type": "timestamp"}, "NameContains": {}, "StatusEquals": {}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["TransformJobSummaries"], "members": {"TransformJobSummaries": {"type": "list", "member": {"type": "structure", "required": ["TransformJobName", "TransformJobArn", "CreationTime", "TransformJobStatus"], "members": {"TransformJobName": {}, "TransformJobArn": {}, "CreationTime": {"type": "timestamp"}, "TransformEndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "TransformJobStatus": {}, "FailureReason": {}}}}, "NextToken": {}}}}, "ListTrialComponents": {"input": {"type": "structure", "members": {"ExperimentName": {}, "TrialName": {}, "SourceArn": {}, "CreatedAfter": {"type": "timestamp"}, "CreatedBefore": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"TrialComponentSummaries": {"type": "list", "member": {"type": "structure", "members": {"TrialComponentName": {}, "TrialComponentArn": {}, "DisplayName": {}, "TrialComponentSource": {"shape": "S19d"}, "Status": {"shape": "Svc"}, "StartTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}}}}, "NextToken": {}}}}, "ListTrials": {"input": {"type": "structure", "members": {"ExperimentName": {}, "TrialComponentName": {}, "CreatedAfter": {"type": "timestamp"}, "CreatedBefore": {"type": "timestamp"}, "SortBy": {}, "SortOrder": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"TrialSummaries": {"type": "list", "member": {"type": "structure", "members": {"TrialArn": {}, "TrialName": {}, "DisplayName": {}, "TrialSource": {"shape": "S199"}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListUserProfiles": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "SortOrder": {}, "SortBy": {}, "DomainIdEquals": {}, "UserProfileNameContains": {}}}, "output": {"type": "structure", "members": {"UserProfiles": {"type": "list", "member": {"type": "structure", "members": {"DomainId": {}, "UserProfileName": {}, "Status": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListWorkforces": {"input": {"type": "structure", "members": {"SortBy": {}, "SortOrder": {}, "NameContains": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["Workforces"], "members": {"Workforces": {"type": "list", "member": {"shape": "S19p"}}, "NextToken": {}}}}, "ListWorkteams": {"input": {"type": "structure", "members": {"SortBy": {}, "SortOrder": {}, "NameContains": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["Workteams"], "members": {"Workteams": {"type": "list", "member": {"shape": "S19x"}}, "NextToken": {}}}}, "PutModelPackageGroupPolicy": {"input": {"type": "structure", "required": ["ModelPackageGroupName", "ResourcePolicy"], "members": {"ModelPackageGroupName": {}, "ResourcePolicy": {}}}, "output": {"type": "structure", "required": ["ModelPackageGroupArn"], "members": {"ModelPackageGroupArn": {}}}}, "QueryLineage": {"input": {"type": "structure", "members": {"StartArns": {"type": "list", "member": {}}, "Direction": {}, "IncludeEdges": {"type": "boolean"}, "Filters": {"type": "structure", "members": {"Types": {"type": "list", "member": {}}, "LineageTypes": {"type": "list", "member": {}}, "CreatedBefore": {"type": "timestamp"}, "CreatedAfter": {"type": "timestamp"}, "ModifiedBefore": {"type": "timestamp"}, "ModifiedAfter": {"type": "timestamp"}, "Properties": {"type": "map", "key": {}, "value": {}}}}, "MaxDepth": {"type": "integer"}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Vertices": {"type": "list", "member": {"type": "structure", "members": {"Arn": {}, "Type": {}, "LineageType": {}}}}, "Edges": {"type": "list", "member": {"type": "structure", "members": {"SourceArn": {}, "DestinationArn": {}, "AssociationType": {}}}}, "NextToken": {}}}}, "RegisterDevices": {"input": {"type": "structure", "required": ["DeviceFleetName", "Devices"], "members": {"DeviceFleetName": {}, "Devices": {"shape": "S1nz"}, "Tags": {"shape": "S7"}}}}, "RenderUiTemplate": {"input": {"type": "structure", "required": ["Task", "RoleArn"], "members": {"UiTemplate": {"shape": "Shv"}, "Task": {"type": "structure", "required": ["Input"], "members": {"Input": {}}}, "RoleArn": {}, "HumanTaskUiArn": {}}}, "output": {"type": "structure", "required": ["RenderedContent", "Errors"], "members": {"RenderedContent": {}, "Errors": {"type": "list", "member": {"type": "structure", "required": ["Code", "Message"], "members": {"Code": {}, "Message": {}}}}}}}, "RetryPipelineExecution": {"input": {"type": "structure", "required": ["PipelineExecutionArn", "ClientRequestToken"], "members": {"PipelineExecutionArn": {}, "ClientRequestToken": {"idempotencyToken": true}, "ParallelismConfiguration": {"shape": "Srf"}}}, "output": {"type": "structure", "members": {"PipelineExecutionArn": {}}}}, "Search": {"input": {"type": "structure", "required": ["Resource"], "members": {"Resource": {}, "SearchExpression": {"shape": "S1oa"}, "SortBy": {}, "SortOrder": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "CrossAccountFilterOption": {}, "VisibilityConditions": {"type": "list", "member": {"type": "structure", "members": {"Key": {}, "Value": {}}}}}}, "output": {"type": "structure", "members": {"Results": {"type": "list", "member": {"type": "structure", "members": {"TrainingJob": {"shape": "S1or"}, "Experiment": {"type": "structure", "members": {"ExperimentName": {}, "ExperimentArn": {}, "DisplayName": {}, "Source": {"shape": "S138"}, "Description": {}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "Tags": {"shape": "S7"}}}, "Trial": {"type": "structure", "members": {"TrialName": {}, "TrialArn": {}, "DisplayName": {}, "ExperimentName": {}, "Source": {"shape": "S199"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "MetadataProperties": {"shape": "S23"}, "Tags": {"shape": "S7"}, "TrialComponentSummaries": {"type": "list", "member": {"type": "structure", "members": {"TrialComponentName": {}, "TrialComponentArn": {}, "TrialComponentSource": {"shape": "S19d"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}}}}}}, "TrialComponent": {"type": "structure", "members": {"TrialComponentName": {}, "DisplayName": {}, "TrialComponentArn": {}, "Source": {"shape": "S19d"}, "Status": {"shape": "Svc"}, "StartTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "Parameters": {"shape": "Svf"}, "InputArtifacts": {"shape": "Svj"}, "OutputArtifacts": {"shape": "Svj"}, "Metrics": {"shape": "S19f"}, "MetadataProperties": {"shape": "S23"}, "SourceDetail": {"type": "structure", "members": {"SourceArn": {}, "TrainingJob": {"shape": "S1or"}, "ProcessingJob": {"type": "structure", "members": {"ProcessingInputs": {"shape": "Srv"}, "ProcessingOutputConfig": {"shape": "Ssi"}, "ProcessingJobName": {}, "ProcessingResources": {"shape": "Ssn"}, "StoppingCondition": {"shape": "Ssp"}, "AppSpecification": {"shape": "Ssr"}, "Environment": {"shape": "Sst"}, "NetworkConfig": {"shape": "Spx"}, "RoleArn": {}, "ExperimentConfig": {"shape": "Ssu"}, "ProcessingJobArn": {}, "ProcessingJobStatus": {}, "ExitMessage": {}, "FailureReason": {}, "ProcessingEndTime": {"type": "timestamp"}, "ProcessingStartTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "MonitoringScheduleArn": {}, "AutoMLJobArn": {}, "TrainingJobArn": {}, "Tags": {"shape": "S7"}}}, "TransformJob": {"shape": "S1oz"}}}, "LineageGroupArn": {}, "Tags": {"shape": "S7"}, "Parents": {"type": "list", "member": {"type": "structure", "members": {"TrialName": {}, "ExperimentName": {}}}}, "RunName": {}}}, "Endpoint": {"type": "structure", "required": ["EndpointName", "EndpointArn", "EndpointConfigName", "EndpointStatus", "CreationTime", "LastModifiedTime"], "members": {"EndpointName": {}, "EndpointArn": {}, "EndpointConfigName": {}, "ProductionVariants": {"shape": "S12q"}, "DataCaptureConfig": {"shape": "S12y"}, "EndpointStatus": {}, "FailureReason": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "MonitoringSchedules": {"type": "list", "member": {"type": "structure", "members": {"MonitoringScheduleArn": {}, "MonitoringScheduleName": {}, "MonitoringScheduleStatus": {}, "MonitoringType": {}, "FailureReason": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "MonitoringScheduleConfig": {"shape": "Spp"}, "EndpointName": {}, "LastMonitoringExecutionSummary": {"shape": "S179"}, "Tags": {"shape": "S7"}}}}, "Tags": {"shape": "S7"}, "ShadowProductionVariants": {"shape": "S12q"}}}, "ModelPackage": {"type": "structure", "members": {"ModelPackageName": {}, "ModelPackageGroupName": {}, "ModelPackageVersion": {"type": "integer"}, "ModelPackageArn": {}, "ModelPackageDescription": {}, "CreationTime": {"type": "timestamp"}, "InferenceSpecification": {"shape": "<PERSON>"}, "SourceAlgorithmSpecification": {"shape": "<PERSON>"}, "ValidationSpecification": {"shape": "Sol"}, "ModelPackageStatus": {}, "ModelPackageStatusDetails": {"shape": "S16w"}, "CertifyForMarketplace": {"type": "boolean"}, "ModelApprovalStatus": {}, "CreatedBy": {"shape": "Sz5"}, "MetadataProperties": {"shape": "S23"}, "ModelMetrics": {"shape": "Sor"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "ApprovalDescription": {}, "Domain": {}, "Task": {}, "SamplePayloadUrl": {}, "AdditionalInferenceSpecifications": {"shape": "Sp7"}, "SourceUri": {}, "SecurityConfig": {"shape": "Spb"}, "ModelCard": {"shape": "Spc"}, "Tags": {"shape": "S7"}, "CustomerMetadataProperties": {"shape": "Soy"}, "DriftCheckBaselines": {"shape": "Sp1"}, "SkipModelValidation": {}}}, "ModelPackageGroup": {"type": "structure", "members": {"ModelPackageGroupName": {}, "ModelPackageGroupArn": {}, "ModelPackageGroupDescription": {}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "ModelPackageGroupStatus": {}, "Tags": {"shape": "S7"}}}, "Pipeline": {"type": "structure", "members": {"PipelineArn": {}, "PipelineName": {}, "PipelineDisplayName": {}, "PipelineDescription": {}, "RoleArn": {}, "PipelineStatus": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "LastRunTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedBy": {"shape": "Sz5"}, "ParallelismConfiguration": {"shape": "Srf"}, "Tags": {"shape": "S7"}}}, "PipelineExecution": {"type": "structure", "members": {"PipelineArn": {}, "PipelineExecutionArn": {}, "PipelineExecutionDisplayName": {}, "PipelineExecutionStatus": {}, "PipelineExecutionDescription": {}, "PipelineExperimentConfig": {"shape": "S17x"}, "FailureReason": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedBy": {"shape": "Sz5"}, "ParallelismConfiguration": {"shape": "Srf"}, "SelectiveExecutionConfig": {"shape": "S17z"}, "PipelineParameters": {"shape": "S1l1"}}}, "FeatureGroup": {"type": "structure", "members": {"FeatureGroupArn": {}, "FeatureGroupName": {}, "RecordIdentifierFeatureName": {}, "EventTimeFeatureName": {}, "FeatureDefinitions": {"shape": "Sfy"}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "OnlineStoreConfig": {"shape": "Sg5"}, "OfflineStoreConfig": {"shape": "Sgb"}, "RoleArn": {}, "FeatureGroupStatus": {}, "OfflineStoreStatus": {"shape": "S13g"}, "LastUpdateStatus": {"shape": "S13j"}, "FailureReason": {}, "Description": {}, "Tags": {"shape": "S7"}}}, "FeatureMetadata": {"type": "structure", "members": {"FeatureGroupArn": {}, "FeatureGroupName": {}, "FeatureName": {}, "FeatureType": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "Description": {}, "Parameters": {"shape": "S13p"}}}, "Project": {"type": "structure", "members": {"ProjectArn": {}, "ProjectName": {}, "ProjectId": {}, "ProjectDescription": {}, "ServiceCatalogProvisioningDetails": {"shape": "Ssz"}, "ServiceCatalogProvisionedProductDetails": {"shape": "S188"}, "ProjectStatus": {}, "CreatedBy": {"shape": "Sz5"}, "CreationTime": {"type": "timestamp"}, "Tags": {"shape": "S7"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}}}, "HyperParameterTuningJob": {"type": "structure", "members": {"HyperParameterTuningJobName": {}, "HyperParameterTuningJobArn": {}, "HyperParameterTuningJobConfig": {"shape": "Si0"}, "TrainingJobDefinition": {"shape": "Sit"}, "TrainingJobDefinitions": {"shape": "Sj9"}, "HyperParameterTuningJobStatus": {}, "CreationTime": {"type": "timestamp"}, "HyperParameterTuningEndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "TrainingJobStatusCounters": {"shape": "S14n"}, "ObjectiveStatusCounters": {"shape": "S14p"}, "BestTrainingJob": {"shape": "S14r"}, "OverallBestTrainingJob": {"shape": "S14r"}, "WarmStartConfig": {"shape": "<PERSON><PERSON>"}, "FailureReason": {}, "TuningJobCompletionDetails": {"shape": "S14u"}, "ConsumedResources": {"shape": "S14v"}, "Tags": {"shape": "S7"}}}, "ModelCard": {"type": "structure", "members": {"ModelCardArn": {}, "ModelCardName": {}, "ModelCardVersion": {"type": "integer"}, "Content": {"shape": "So6"}, "ModelCardStatus": {}, "SecurityConfig": {"shape": "So5"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "Tags": {"shape": "S7"}, "ModelId": {}, "RiskRating": {}, "ModelPackageGroupName": {}}}, "Model": {"type": "structure", "members": {"Model": {"type": "structure", "members": {"ModelName": {}, "PrimaryContainer": {"shape": "Sni"}, "Containers": {"shape": "<PERSON><PERSON>"}, "InferenceExecutionConfig": {"shape": "Snv"}, "ExecutionRoleArn": {}, "VpcConfig": {"shape": "S6d"}, "CreationTime": {"type": "timestamp"}, "ModelArn": {}, "EnableNetworkIsolation": {"type": "boolean"}, "Tags": {"shape": "S7"}, "DeploymentRecommendation": {"shape": "S16f"}}}, "Endpoints": {"type": "list", "member": {"type": "structure", "required": ["EndpointName", "EndpointArn", "CreationTime", "LastModifiedTime", "EndpointStatus"], "members": {"EndpointName": {}, "EndpointArn": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "EndpointStatus": {}}}}, "LastBatchTransformJob": {"shape": "S1oz"}, "MonitoringSchedules": {"type": "list", "member": {"type": "structure", "members": {"MonitoringScheduleArn": {}, "MonitoringScheduleName": {}, "MonitoringScheduleStatus": {}, "MonitoringType": {}, "FailureReason": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "MonitoringScheduleConfig": {"shape": "Spp"}, "EndpointName": {}, "MonitoringAlertSummaries": {"shape": "S1ix"}, "LastMonitoringExecutionSummary": {"shape": "S179"}, "BatchTransformInput": {"shape": "S9x"}}}}, "ModelCard": {"type": "structure", "members": {"ModelCardArn": {}, "ModelCardName": {}, "ModelCardVersion": {"type": "integer"}, "ModelCardStatus": {}, "SecurityConfig": {"shape": "So5"}, "CreationTime": {"type": "timestamp"}, "CreatedBy": {"shape": "Sz5"}, "LastModifiedTime": {"type": "timestamp"}, "LastModifiedBy": {"shape": "Sz5"}, "Tags": {"shape": "S7"}, "ModelId": {}, "RiskRating": {}}}}}}}}, "NextToken": {}}}}, "SendPipelineExecutionStepFailure": {"input": {"type": "structure", "required": ["CallbackToken"], "members": {"CallbackToken": {}, "FailureReason": {}, "ClientRequestToken": {"idempotencyToken": true}}}, "output": {"type": "structure", "members": {"PipelineExecutionArn": {}}}}, "SendPipelineExecutionStepSuccess": {"input": {"type": "structure", "required": ["CallbackToken"], "members": {"CallbackToken": {}, "OutputParameters": {"shape": "S1ki"}, "ClientRequestToken": {"idempotencyToken": true}}}, "output": {"type": "structure", "members": {"PipelineExecutionArn": {}}}}, "StartEdgeDeploymentStage": {"input": {"type": "structure", "required": ["EdgeDeploymentPlanName", "StageName"], "members": {"EdgeDeploymentPlanName": {}, "StageName": {}}}}, "StartInferenceExperiment": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "required": ["InferenceExperimentArn"], "members": {"InferenceExperimentArn": {}}}}, "StartMlflowTrackingServer": {"input": {"type": "structure", "required": ["TrackingServerName"], "members": {"TrackingServerName": {}}}, "output": {"type": "structure", "members": {"TrackingServerArn": {}}}}, "StartMonitoringSchedule": {"input": {"type": "structure", "required": ["MonitoringScheduleName"], "members": {"MonitoringScheduleName": {}}}}, "StartNotebookInstance": {"input": {"type": "structure", "required": ["NotebookInstanceName"], "members": {"NotebookInstanceName": {}}}}, "StartPipelineExecution": {"input": {"type": "structure", "required": ["PipelineName", "ClientRequestToken"], "members": {"PipelineName": {}, "PipelineExecutionDisplayName": {}, "PipelineParameters": {"shape": "S1l1"}, "PipelineExecutionDescription": {}, "ClientRequestToken": {"idempotencyToken": true}, "ParallelismConfiguration": {"shape": "Srf"}, "SelectiveExecutionConfig": {"shape": "S17z"}}}, "output": {"type": "structure", "members": {"PipelineExecutionArn": {}}}}, "StopAutoMLJob": {"input": {"type": "structure", "required": ["AutoMLJobName"], "members": {"AutoMLJobName": {}}}}, "StopCompilationJob": {"input": {"type": "structure", "required": ["CompilationJobName"], "members": {"CompilationJobName": {}}}}, "StopEdgeDeploymentStage": {"input": {"type": "structure", "required": ["EdgeDeploymentPlanName", "StageName"], "members": {"EdgeDeploymentPlanName": {}, "StageName": {}}}}, "StopEdgePackagingJob": {"input": {"type": "structure", "required": ["EdgePackagingJobName"], "members": {"EdgePackagingJobName": {}}}}, "StopHyperParameterTuningJob": {"input": {"type": "structure", "required": ["HyperParameterTuningJobName"], "members": {"HyperParameterTuningJobName": {}}}}, "StopInferenceExperiment": {"input": {"type": "structure", "required": ["Name", "ModelVariantActions"], "members": {"Name": {}, "ModelVariantActions": {"type": "map", "key": {}, "value": {}}, "DesiredModelVariants": {"shape": "Ski"}, "DesiredState": {}, "Reason": {}}}, "output": {"type": "structure", "required": ["InferenceExperimentArn"], "members": {"InferenceExperimentArn": {}}}}, "StopInferenceRecommendationsJob": {"input": {"type": "structure", "required": ["JobName"], "members": {"JobName": {}}}}, "StopLabelingJob": {"input": {"type": "structure", "required": ["LabelingJobName"], "members": {"LabelingJobName": {}}}}, "StopMlflowTrackingServer": {"input": {"type": "structure", "required": ["TrackingServerName"], "members": {"TrackingServerName": {}}}, "output": {"type": "structure", "members": {"TrackingServerArn": {}}}}, "StopMonitoringSchedule": {"input": {"type": "structure", "required": ["MonitoringScheduleName"], "members": {"MonitoringScheduleName": {}}}}, "StopNotebookInstance": {"input": {"type": "structure", "required": ["NotebookInstanceName"], "members": {"NotebookInstanceName": {}}}}, "StopOptimizationJob": {"input": {"type": "structure", "required": ["OptimizationJobName"], "members": {"OptimizationJobName": {}}}}, "StopPipelineExecution": {"input": {"type": "structure", "required": ["PipelineExecutionArn", "ClientRequestToken"], "members": {"PipelineExecutionArn": {}, "ClientRequestToken": {"idempotencyToken": true}}}, "output": {"type": "structure", "members": {"PipelineExecutionArn": {}}}}, "StopProcessingJob": {"input": {"type": "structure", "required": ["ProcessingJobName"], "members": {"ProcessingJobName": {}}}}, "StopTrainingJob": {"input": {"type": "structure", "required": ["TrainingJobName"], "members": {"TrainingJobName": {}}}}, "StopTransformJob": {"input": {"type": "structure", "required": ["TransformJobName"], "members": {"TransformJobName": {}}}}, "UpdateAction": {"input": {"type": "structure", "required": ["ActionName"], "members": {"ActionName": {}, "Description": {}, "Status": {}, "Properties": {"shape": "S21"}, "PropertiesToRemove": {"shape": "S1ql"}}}, "output": {"type": "structure", "members": {"ActionArn": {}}}}, "UpdateAppImageConfig": {"input": {"type": "structure", "required": ["AppImageConfigName"], "members": {"AppImageConfigName": {}, "KernelGatewayImageConfig": {"shape": "S51"}, "JupyterLabAppImageConfig": {"shape": "S5a"}, "CodeEditorAppImageConfig": {"shape": "S5h"}}}, "output": {"type": "structure", "members": {"AppImageConfigArn": {}}}}, "UpdateArtifact": {"input": {"type": "structure", "required": ["ArtifactArn"], "members": {"ArtifactArn": {}, "ArtifactName": {}, "Properties": {"shape": "S5p"}, "PropertiesToRemove": {"shape": "S1ql"}}}, "output": {"type": "structure", "members": {"ArtifactArn": {}}}}, "UpdateCluster": {"input": {"type": "structure", "required": ["ClusterName", "InstanceGroups"], "members": {"ClusterName": {}, "InstanceGroups": {"shape": "S81"}}}, "output": {"type": "structure", "required": ["ClusterArn"], "members": {"ClusterArn": {}}}}, "UpdateClusterSoftware": {"input": {"type": "structure", "required": ["ClusterName"], "members": {"ClusterName": {}}}, "output": {"type": "structure", "required": ["ClusterArn"], "members": {"ClusterArn": {}}}}, "UpdateCodeRepository": {"input": {"type": "structure", "required": ["CodeRepositoryName"], "members": {"CodeRepositoryName": {}, "GitConfig": {"type": "structure", "members": {"SecretArn": {}}}}}, "output": {"type": "structure", "required": ["CodeRepositoryArn"], "members": {"CodeRepositoryArn": {}}}}, "UpdateContext": {"input": {"type": "structure", "required": ["ContextName"], "members": {"ContextName": {}, "Description": {}, "Properties": {"shape": "S21"}, "PropertiesToRemove": {"shape": "S1ql"}}}, "output": {"type": "structure", "members": {"ContextArn": {}}}}, "UpdateDeviceFleet": {"input": {"type": "structure", "required": ["DeviceFleetName", "OutputConfig"], "members": {"DeviceFleetName": {}, "RoleArn": {}, "Description": {}, "OutputConfig": {"shape": "Sal"}, "EnableIotRoleAlias": {"type": "boolean"}}}}, "UpdateDevices": {"input": {"type": "structure", "required": ["DeviceFleetName", "Devices"], "members": {"DeviceFleetName": {}, "Devices": {"shape": "S1nz"}}}}, "UpdateDomain": {"input": {"type": "structure", "required": ["DomainId"], "members": {"DomainId": {}, "DefaultUserSettings": {"shape": "Sar"}, "DomainSettingsForUpdate": {"type": "structure", "members": {"RStudioServerProDomainSettingsForUpdate": {"type": "structure", "required": ["DomainExecutionRoleArn"], "members": {"DomainExecutionRoleArn": {}, "DefaultResourceSpec": {"shape": "S4r"}, "RStudioConnectUrl": {}, "RStudioPackageManagerUrl": {}}}, "ExecutionRoleIdentityConfig": {}, "SecurityGroupIds": {"shape": "Scd"}, "DockerSettings": {"shape": "Scg"}, "AmazonQSettings": {"shape": "Scj"}}}, "AppSecurityGroupManagement": {}, "DefaultSpaceSettings": {"shape": "Sco"}, "SubnetIds": {"shape": "S6g"}, "AppNetworkAccessType": {}}}, "output": {"type": "structure", "members": {"DomainArn": {}}}}, "UpdateEndpoint": {"input": {"type": "structure", "required": ["EndpointName", "EndpointConfigName"], "members": {"EndpointName": {}, "EndpointConfigName": {}, "RetainAllVariantProperties": {"type": "boolean"}, "ExcludeRetainedVariantProperties": {"type": "list", "member": {"type": "structure", "required": ["VariantPropertyType"], "members": {"VariantPropertyType": {}}}}, "DeploymentConfig": {"shape": "Sdb"}, "RetainDeploymentConfig": {"type": "boolean"}}}, "output": {"type": "structure", "required": ["EndpointArn"], "members": {"EndpointArn": {}}}}, "UpdateEndpointWeightsAndCapacities": {"input": {"type": "structure", "required": ["EndpointName", "DesiredWeightsAndCapacities"], "members": {"EndpointName": {}, "DesiredWeightsAndCapacities": {"type": "list", "member": {"type": "structure", "required": ["VariantName"], "members": {"VariantName": {}, "DesiredWeight": {"type": "float"}, "DesiredInstanceCount": {"type": "integer"}, "ServerlessUpdateConfig": {"type": "structure", "members": {"MaxConcurrency": {"type": "integer"}, "ProvisionedConcurrency": {"type": "integer"}}}}}}}}, "output": {"type": "structure", "required": ["EndpointArn"], "members": {"EndpointArn": {}}}}, "UpdateExperiment": {"input": {"type": "structure", "required": ["ExperimentName"], "members": {"ExperimentName": {}, "DisplayName": {}, "Description": {}}}, "output": {"type": "structure", "members": {"ExperimentArn": {}}}}, "UpdateFeatureGroup": {"input": {"type": "structure", "required": ["FeatureGroupName"], "members": {"FeatureGroupName": {}, "FeatureAdditions": {"type": "list", "member": {"shape": "Sfz"}}, "OnlineStoreConfig": {"type": "structure", "members": {"TtlDuration": {"shape": "Sg7"}}}, "ThroughputConfig": {"type": "structure", "members": {"ThroughputMode": {}, "ProvisionedReadCapacityUnits": {"type": "integer"}, "ProvisionedWriteCapacityUnits": {"type": "integer"}}}}}, "output": {"type": "structure", "required": ["FeatureGroupArn"], "members": {"FeatureGroupArn": {}}}}, "UpdateFeatureMetadata": {"input": {"type": "structure", "required": ["FeatureGroupName", "FeatureName"], "members": {"FeatureGroupName": {}, "FeatureName": {}, "Description": {}, "ParameterAdditions": {"type": "list", "member": {"shape": "S13q"}}, "ParameterRemovals": {"type": "list", "member": {}}}}}, "UpdateHub": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"HubName": {}, "HubDescription": {}, "HubDisplayName": {}, "HubSearchKeywords": {"shape": "Shh"}}}, "output": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"HubArn": {}}}}, "UpdateImage": {"input": {"type": "structure", "required": ["ImageName"], "members": {"DeleteProperties": {"type": "list", "member": {}}, "Description": {}, "DisplayName": {}, "ImageName": {}, "RoleArn": {}}}, "output": {"type": "structure", "members": {"ImageArn": {}}}}, "UpdateImageVersion": {"input": {"type": "structure", "required": ["ImageName"], "members": {"ImageName": {}, "Alias": {}, "Version": {"type": "integer"}, "AliasesToAdd": {"shape": "Sjp"}, "AliasesToDelete": {"shape": "Sjp"}, "VendorGuidance": {}, "JobType": {}, "MLFramework": {}, "ProgrammingLang": {}, "Processor": {}, "Horovod": {"type": "boolean"}, "ReleaseNotes": {}}}, "output": {"type": "structure", "members": {"ImageVersionArn": {}}}}, "UpdateInferenceComponent": {"input": {"type": "structure", "required": ["InferenceComponentName"], "members": {"InferenceComponentName": {}, "Specification": {"shape": "Sk1"}, "RuntimeConfig": {"shape": "Sk8"}}}, "output": {"type": "structure", "required": ["InferenceComponentArn"], "members": {"InferenceComponentArn": {}}}}, "UpdateInferenceComponentRuntimeConfig": {"input": {"type": "structure", "required": ["InferenceComponentName", "DesiredRuntimeConfig"], "members": {"InferenceComponentName": {}, "DesiredRuntimeConfig": {"shape": "Sk8"}}}, "output": {"type": "structure", "required": ["InferenceComponentArn"], "members": {"InferenceComponentArn": {}}}}, "UpdateInferenceExperiment": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Schedule": {"shape": "Skf"}, "Description": {}, "ModelVariants": {"shape": "Ski"}, "DataStorageConfig": {"shape": "Skq"}, "ShadowModeConfig": {"shape": "Skr"}}}, "output": {"type": "structure", "required": ["InferenceExperimentArn"], "members": {"InferenceExperimentArn": {}}}}, "UpdateMlflowTrackingServer": {"input": {"type": "structure", "required": ["TrackingServerName"], "members": {"TrackingServerName": {}, "ArtifactStoreUri": {}, "TrackingServerSize": {}, "AutomaticModelRegistration": {"type": "boolean"}, "WeeklyMaintenanceWindowStart": {}}}, "output": {"type": "structure", "members": {"TrackingServerArn": {}}}}, "UpdateModelCard": {"input": {"type": "structure", "required": ["ModelCardName"], "members": {"ModelCardName": {}, "Content": {"shape": "So6"}, "ModelCardStatus": {}}}, "output": {"type": "structure", "required": ["ModelCardArn"], "members": {"ModelCardArn": {}}}}, "UpdateModelPackage": {"input": {"type": "structure", "required": ["ModelPackageArn"], "members": {"ModelPackageArn": {}, "ModelApprovalStatus": {}, "ApprovalDescription": {}, "CustomerMetadataProperties": {"shape": "Soy"}, "CustomerMetadataPropertiesToRemove": {"type": "list", "member": {}}, "AdditionalInferenceSpecificationsToAdd": {"shape": "Sp7"}, "InferenceSpecification": {"shape": "<PERSON>"}, "SourceUri": {}, "ModelCard": {"shape": "Spc"}}}, "output": {"type": "structure", "required": ["ModelPackageArn"], "members": {"ModelPackageArn": {}}}}, "UpdateMonitoringAlert": {"input": {"type": "structure", "required": ["MonitoringScheduleName", "MonitoringAlertName", "DatapointsToAlert", "EvaluationPeriod"], "members": {"MonitoringScheduleName": {}, "MonitoringAlertName": {}, "DatapointsToAlert": {"type": "integer"}, "EvaluationPeriod": {"type": "integer"}}}, "output": {"type": "structure", "required": ["MonitoringScheduleArn"], "members": {"MonitoringScheduleArn": {}, "MonitoringAlertName": {}}}}, "UpdateMonitoringSchedule": {"input": {"type": "structure", "required": ["MonitoringScheduleName", "MonitoringScheduleConfig"], "members": {"MonitoringScheduleName": {}, "MonitoringScheduleConfig": {"shape": "Spp"}}}, "output": {"type": "structure", "required": ["MonitoringScheduleArn"], "members": {"MonitoringScheduleArn": {}}}}, "UpdateNotebookInstance": {"input": {"type": "structure", "required": ["NotebookInstanceName"], "members": {"NotebookInstanceName": {}, "InstanceType": {}, "RoleArn": {}, "LifecycleConfigName": {}, "DisassociateLifecycleConfig": {"type": "boolean"}, "VolumeSizeInGB": {"type": "integer"}, "DefaultCodeRepository": {}, "AdditionalCodeRepositories": {"shape": "Sq9"}, "AcceleratorTypes": {"shape": "Sq6"}, "DisassociateAcceleratorTypes": {"type": "boolean"}, "DisassociateDefaultCodeRepository": {"type": "boolean"}, "DisassociateAdditionalCodeRepositories": {"type": "boolean"}, "RootAccess": {}, "InstanceMetadataServiceConfiguration": {"shape": "Sqc"}}}, "output": {"type": "structure", "members": {}}}, "UpdateNotebookInstanceLifecycleConfig": {"input": {"type": "structure", "required": ["NotebookInstanceLifecycleConfigName"], "members": {"NotebookInstanceLifecycleConfigName": {}, "OnCreate": {"shape": "Sqh"}, "OnStart": {"shape": "Sqh"}}}, "output": {"type": "structure", "members": {}}}, "UpdatePipeline": {"input": {"type": "structure", "required": ["PipelineName"], "members": {"PipelineName": {}, "PipelineDisplayName": {}, "PipelineDefinition": {}, "PipelineDefinitionS3Location": {"shape": "Sr9"}, "PipelineDescription": {}, "RoleArn": {}, "ParallelismConfiguration": {"shape": "Srf"}}}, "output": {"type": "structure", "members": {"PipelineArn": {}}}}, "UpdatePipelineExecution": {"input": {"type": "structure", "required": ["PipelineExecutionArn"], "members": {"PipelineExecutionArn": {}, "PipelineExecutionDescription": {}, "PipelineExecutionDisplayName": {}, "ParallelismConfiguration": {"shape": "Srf"}}}, "output": {"type": "structure", "members": {"PipelineExecutionArn": {}}}}, "UpdateProject": {"input": {"type": "structure", "required": ["ProjectName"], "members": {"ProjectName": {}, "ProjectDescription": {}, "ServiceCatalogProvisioningUpdateDetails": {"type": "structure", "members": {"ProvisioningArtifactId": {}, "ProvisioningParameters": {"shape": "St1"}}}, "Tags": {"shape": "S7"}}}, "output": {"type": "structure", "required": ["ProjectArn"], "members": {"ProjectArn": {}}}}, "UpdateSpace": {"input": {"type": "structure", "required": ["DomainId", "SpaceName"], "members": {"DomainId": {}, "SpaceName": {}, "SpaceSettings": {"shape": "St9"}, "SpaceDisplayName": {}}}, "output": {"type": "structure", "members": {"SpaceArn": {}}}}, "UpdateTrainingJob": {"input": {"type": "structure", "required": ["TrainingJobName"], "members": {"TrainingJobName": {}, "ProfilerConfig": {"type": "structure", "members": {"S3OutputPath": {}, "ProfilingIntervalInMilliseconds": {"type": "long"}, "ProfilingParameters": {"shape": "<PERSON><PERSON>"}, "DisableProfiler": {"type": "boolean"}}}, "ProfilerRuleConfigurations": {"shape": "Sul"}, "ResourceConfig": {"type": "structure", "required": ["KeepAlivePeriodInSeconds"], "members": {"KeepAlivePeriodInSeconds": {"type": "integer"}}}, "RemoteDebugConfig": {"type": "structure", "members": {"EnableRemoteDebug": {"type": "boolean"}}}}}, "output": {"type": "structure", "required": ["TrainingJobArn"], "members": {"TrainingJobArn": {}}}}, "UpdateTrial": {"input": {"type": "structure", "required": ["TrialName"], "members": {"TrialName": {}, "DisplayName": {}}}, "output": {"type": "structure", "members": {"TrialArn": {}}}}, "UpdateTrialComponent": {"input": {"type": "structure", "required": ["TrialComponentName"], "members": {"TrialComponentName": {}, "DisplayName": {}, "Status": {"shape": "Svc"}, "StartTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "Parameters": {"shape": "Svf"}, "ParametersToRemove": {"shape": "S1t4"}, "InputArtifacts": {"shape": "Svj"}, "InputArtifactsToRemove": {"shape": "S1t4"}, "OutputArtifacts": {"shape": "Svj"}, "OutputArtifactsToRemove": {"shape": "S1t4"}}}, "output": {"type": "structure", "members": {"TrialComponentArn": {}}}}, "UpdateUserProfile": {"input": {"type": "structure", "required": ["DomainId", "UserProfileName"], "members": {"DomainId": {}, "UserProfileName": {}, "UserSettings": {"shape": "Sar"}}}, "output": {"type": "structure", "members": {"UserProfileArn": {}}}}, "UpdateWorkforce": {"input": {"type": "structure", "required": ["WorkforceName"], "members": {"WorkforceName": {}, "SourceIpConfig": {"shape": "Sw4"}, "OidcConfig": {"shape": "Svx"}, "WorkforceVpcConfig": {"shape": "Sw8"}}}, "output": {"type": "structure", "required": ["Workforce"], "members": {"Workforce": {"shape": "S19p"}}}}, "UpdateWorkteam": {"input": {"type": "structure", "required": ["WorkteamName"], "members": {"WorkteamName": {}, "MemberDefinitions": {"shape": "Swi"}, "Description": {}, "NotificationConfiguration": {"shape": "Swq"}, "WorkerAccessConfiguration": {"shape": "Sws"}}}, "output": {"type": "structure", "required": ["Workteam"], "members": {"Workteam": {"shape": "S19x"}}}}}, "shapes": {"S7": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {}}}}, "Sr": {"type": "structure", "required": ["Containers"], "members": {"Containers": {"shape": "Ss"}, "SupportedTransformInstanceTypes": {"shape": "S1j"}, "SupportedRealtimeInferenceInstanceTypes": {"shape": "S1l"}, "SupportedContentTypes": {"shape": "S1n"}, "SupportedResponseMIMETypes": {"shape": "S1p"}}}, "Ss": {"type": "list", "member": {"type": "structure", "required": ["Image"], "members": {"ContainerHostname": {}, "Image": {}, "ImageDigest": {}, "ModelDataUrl": {}, "ModelDataSource": {"shape": "Sy"}, "ProductId": {}, "Environment": {"shape": "S18"}, "ModelInput": {"type": "structure", "required": ["DataInputConfig"], "members": {"DataInputConfig": {}}}, "Framework": {}, "FrameworkVersion": {}, "NearestModelName": {}, "AdditionalS3DataSource": {"shape": "S1f"}}}}, "Sy": {"type": "structure", "members": {"S3DataSource": {"shape": "Sz"}}}, "Sz": {"type": "structure", "required": ["S3Uri", "S3DataType", "CompressionType"], "members": {"S3Uri": {}, "S3DataType": {}, "CompressionType": {}, "ModelAccessConfig": {"shape": "S13"}, "HubAccessConfig": {"type": "structure", "required": ["HubContentArn"], "members": {"HubContentArn": {}}}}}, "S13": {"type": "structure", "required": ["AcceptEula"], "members": {"AcceptEula": {"type": "boolean"}}}, "S18": {"type": "map", "key": {}, "value": {}}, "S1f": {"type": "structure", "required": ["S3DataType", "S3Uri"], "members": {"S3DataType": {}, "S3Uri": {}, "CompressionType": {}}}, "S1j": {"type": "list", "member": {}}, "S1l": {"type": "list", "member": {}}, "S1n": {"type": "list", "member": {}}, "S1p": {"type": "list", "member": {}}, "S1w": {"type": "structure", "required": ["SourceUri"], "members": {"SourceUri": {}, "SourceType": {}, "SourceId": {}}}, "S21": {"type": "map", "key": {}, "value": {}}, "S23": {"type": "structure", "members": {"CommitId": {}, "Repository": {}, "GeneratedBy": {}, "ProjectId": {}}}, "S28": {"type": "structure", "required": ["TrainingImage", "SupportedTrainingInstanceTypes", "TrainingChannels"], "members": {"TrainingImage": {}, "TrainingImageDigest": {}, "SupportedHyperParameters": {"type": "list", "member": {"type": "structure", "required": ["Name", "Type"], "members": {"Name": {}, "Description": {}, "Type": {}, "Range": {"type": "structure", "members": {"IntegerParameterRangeSpecification": {"type": "structure", "required": ["MinValue", "MaxValue"], "members": {"MinValue": {}, "MaxValue": {}}}, "ContinuousParameterRangeSpecification": {"type": "structure", "required": ["MinValue", "MaxValue"], "members": {"MinValue": {}, "MaxValue": {}}}, "CategoricalParameterRangeSpecification": {"type": "structure", "required": ["Values"], "members": {"Values": {"shape": "S2i"}}}}}, "IsTunable": {"type": "boolean"}, "IsRequired": {"type": "boolean"}, "DefaultValue": {}}}}, "SupportedTrainingInstanceTypes": {"type": "list", "member": {}}, "SupportsDistributedTraining": {"type": "boolean"}, "MetricDefinitions": {"shape": "S2n"}, "TrainingChannels": {"type": "list", "member": {"type": "structure", "required": ["Name", "SupportedContentTypes", "SupportedInputModes"], "members": {"Name": {}, "Description": {}, "IsRequired": {"type": "boolean"}, "SupportedContentTypes": {"shape": "S1n"}, "SupportedCompressionTypes": {"type": "list", "member": {}}, "SupportedInputModes": {"type": "list", "member": {}}}}}, "SupportedTuningJobObjectiveMetrics": {"type": "list", "member": {"shape": "S2y"}}, "AdditionalS3DataSource": {"shape": "S1f"}}}, "S2i": {"type": "list", "member": {}}, "S2n": {"type": "list", "member": {"type": "structure", "required": ["Name", "Regex"], "members": {"Name": {}, "Regex": {}}}}, "S2y": {"type": "structure", "required": ["Type", "MetricName"], "members": {"Type": {}, "MetricName": {}}}, "S30": {"type": "structure", "required": ["ValidationRole", "ValidationProfiles"], "members": {"ValidationRole": {}, "ValidationProfiles": {"type": "list", "member": {"type": "structure", "required": ["ProfileName", "TrainingJobDefinition"], "members": {"ProfileName": {}, "TrainingJobDefinition": {"type": "structure", "required": ["TrainingInputMode", "InputDataConfig", "OutputDataConfig", "ResourceConfig", "StoppingCondition"], "members": {"TrainingInputMode": {}, "HyperParameters": {"shape": "S35"}, "InputDataConfig": {"shape": "S37"}, "OutputDataConfig": {"shape": "S3p"}, "ResourceConfig": {"shape": "S3s"}, "StoppingCondition": {"shape": "S3y"}}}, "TransformJobDefinition": {"shape": "S42"}}}}}}, "S35": {"type": "map", "key": {}, "value": {}}, "S37": {"type": "list", "member": {"type": "structure", "required": ["ChannelName", "DataSource"], "members": {"ChannelName": {}, "DataSource": {"type": "structure", "members": {"S3DataSource": {"type": "structure", "required": ["S3DataType", "S3Uri"], "members": {"S3DataType": {}, "S3Uri": {}, "S3DataDistributionType": {}, "AttributeNames": {"type": "list", "member": {}}, "InstanceGroupNames": {"type": "list", "member": {}}}}, "FileSystemDataSource": {"type": "structure", "required": ["FileSystemId", "FileSystemAccessMode", "FileSystemType", "DirectoryPath"], "members": {"FileSystemId": {}, "FileSystemAccessMode": {}, "FileSystemType": {}, "DirectoryPath": {}}}}}, "ContentType": {}, "CompressionType": {}, "RecordWrapperType": {}, "InputMode": {}, "ShuffleConfig": {"type": "structure", "required": ["Seed"], "members": {"Seed": {"type": "long"}}}}}}, "S3p": {"type": "structure", "required": ["S3OutputPath"], "members": {"KmsKeyId": {}, "S3OutputPath": {}, "CompressionType": {}}}, "S3s": {"type": "structure", "required": ["VolumeSizeInGB"], "members": {"InstanceType": {}, "InstanceCount": {"type": "integer"}, "VolumeSizeInGB": {"type": "integer"}, "VolumeKmsKeyId": {}, "KeepAlivePeriodInSeconds": {"type": "integer"}, "InstanceGroups": {"type": "list", "member": {"type": "structure", "required": ["InstanceType", "InstanceCount", "InstanceGroupName"], "members": {"InstanceType": {}, "InstanceCount": {"type": "integer"}, "InstanceGroupName": {}}}}}}, "S3y": {"type": "structure", "members": {"MaxRuntimeInSeconds": {"type": "integer"}, "MaxWaitTimeInSeconds": {"type": "integer"}, "MaxPendingTimeInSeconds": {"type": "integer"}}}, "S42": {"type": "structure", "required": ["TransformInput", "TransformOutput", "TransformResources"], "members": {"MaxConcurrentTransforms": {"type": "integer"}, "MaxPayloadInMB": {"type": "integer"}, "BatchStrategy": {}, "Environment": {"shape": "S46"}, "TransformInput": {"shape": "S49"}, "TransformOutput": {"shape": "S4d"}, "TransformResources": {"shape": "S4g"}}}, "S46": {"type": "map", "key": {}, "value": {}}, "S49": {"type": "structure", "required": ["DataSource"], "members": {"DataSource": {"type": "structure", "required": ["S3DataSource"], "members": {"S3DataSource": {"type": "structure", "required": ["S3DataType", "S3Uri"], "members": {"S3DataType": {}, "S3Uri": {}}}}}, "ContentType": {}, "CompressionType": {}, "SplitType": {}}}, "S4d": {"type": "structure", "required": ["S3OutputPath"], "members": {"S3OutputPath": {}, "Accept": {}, "AssembleWith": {}, "KmsKeyId": {}}}, "S4g": {"type": "structure", "required": ["InstanceType", "InstanceCount"], "members": {"InstanceType": {}, "InstanceCount": {"type": "integer"}, "VolumeKmsKeyId": {}}}, "S4r": {"type": "structure", "members": {"SageMakerImageArn": {}, "SageMakerImageVersionArn": {}, "SageMakerImageVersionAlias": {}, "InstanceType": {}, "LifecycleConfigArn": {}}}, "S51": {"type": "structure", "required": ["KernelSpecs"], "members": {"KernelSpecs": {"type": "list", "member": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "DisplayName": {}}}}, "FileSystemConfig": {"shape": "S56"}}}, "S56": {"type": "structure", "members": {"MountPath": {}, "DefaultUid": {"type": "integer"}, "DefaultGid": {"type": "integer"}}}, "S5a": {"type": "structure", "members": {"FileSystemConfig": {"shape": "S56"}, "ContainerConfig": {"shape": "S5b"}}}, "S5b": {"type": "structure", "members": {"ContainerArguments": {"type": "list", "member": {}}, "ContainerEntrypoint": {"type": "list", "member": {}}, "ContainerEnvironmentVariables": {"type": "map", "key": {}, "value": {}}}}, "S5h": {"type": "structure", "members": {"FileSystemConfig": {"shape": "S56"}, "ContainerConfig": {"shape": "S5b"}}}, "S5l": {"type": "structure", "required": ["SourceUri"], "members": {"SourceUri": {}, "SourceTypes": {"type": "list", "member": {"type": "structure", "required": ["SourceIdType", "Value"], "members": {"SourceIdType": {}, "Value": {}}}}}}, "S5p": {"type": "map", "key": {}, "value": {}}, "S5v": {"type": "list", "member": {"type": "structure", "required": ["TargetAttributeName"], "members": {"DataSource": {"shape": "S5x"}, "CompressionType": {}, "TargetAttributeName": {}, "ContentType": {}, "ChannelType": {}, "SampleWeightAttributeName": {}}}}, "S5x": {"type": "structure", "required": ["S3DataSource"], "members": {"S3DataSource": {"type": "structure", "required": ["S3DataType", "S3Uri"], "members": {"S3DataType": {}, "S3Uri": {}}}}}, "S63": {"type": "structure", "required": ["S3OutputPath"], "members": {"KmsKeyId": {}, "S3OutputPath": {}}}, "S65": {"type": "structure", "required": ["MetricName"], "members": {"MetricName": {}}}, "S67": {"type": "structure", "members": {"CompletionCriteria": {"shape": "S68"}, "SecurityConfig": {"shape": "S6c"}, "CandidateGenerationConfig": {"type": "structure", "members": {"FeatureSpecificationS3Uri": {}, "AlgorithmsConfig": {"shape": "S6j"}}}, "DataSplitConfig": {"shape": "S6n"}, "Mode": {}}}, "S68": {"type": "structure", "members": {"MaxCandidates": {"type": "integer"}, "MaxRuntimePerTrainingJobInSeconds": {"type": "integer"}, "MaxAutoMLJobRuntimeInSeconds": {"type": "integer"}}}, "S6c": {"type": "structure", "members": {"VolumeKmsKeyId": {}, "EnableInterContainerTrafficEncryption": {"type": "boolean"}, "VpcConfig": {"shape": "S6d"}}}, "S6d": {"type": "structure", "required": ["SecurityGroupIds", "Subnets"], "members": {"SecurityGroupIds": {"type": "list", "member": {}}, "Subnets": {"shape": "S6g"}}}, "S6g": {"type": "list", "member": {}}, "S6j": {"type": "list", "member": {"type": "structure", "required": ["AutoMLAlgorithms"], "members": {"AutoMLAlgorithms": {"type": "list", "member": {}}}}}, "S6n": {"type": "structure", "members": {"ValidationFraction": {"type": "float"}}}, "S6r": {"type": "structure", "members": {"AutoGenerateEndpointName": {"type": "boolean"}, "EndpointName": {}}}, "S6x": {"type": "list", "member": {"type": "structure", "members": {"ChannelType": {}, "ContentType": {}, "CompressionType": {}, "DataSource": {"shape": "S5x"}}}}, "S6z": {"type": "structure", "members": {"ImageClassificationJobConfig": {"type": "structure", "members": {"CompletionCriteria": {"shape": "S68"}}}, "TextClassificationJobConfig": {"type": "structure", "required": ["ContentColumn", "TargetLabelColumn"], "members": {"CompletionCriteria": {"shape": "S68"}, "ContentColumn": {}, "TargetLabelColumn": {}}}, "TimeSeriesForecastingJobConfig": {"type": "structure", "required": ["ForecastFrequency", "ForecastHorizon", "TimeSeriesConfig"], "members": {"FeatureSpecificationS3Uri": {}, "CompletionCriteria": {"shape": "S68"}, "ForecastFrequency": {}, "ForecastHorizon": {"type": "integer"}, "ForecastQuantiles": {"type": "list", "member": {}}, "Transformations": {"type": "structure", "members": {"Filling": {"type": "map", "key": {}, "value": {"type": "map", "key": {}, "value": {}}}, "Aggregation": {"type": "map", "key": {}, "value": {}}}}, "TimeSeriesConfig": {"type": "structure", "required": ["TargetAttributeName", "TimestampAttributeName", "ItemIdentifierAttributeName"], "members": {"TargetAttributeName": {}, "TimestampAttributeName": {}, "ItemIdentifierAttributeName": {}, "GroupingAttributeNames": {"type": "list", "member": {}}}}, "HolidayConfig": {"type": "list", "member": {"type": "structure", "members": {"CountryCode": {}}}}, "CandidateGenerationConfig": {"shape": "S7p"}}}, "TabularJobConfig": {"type": "structure", "required": ["TargetAttributeName"], "members": {"CandidateGenerationConfig": {"shape": "S7p"}, "CompletionCriteria": {"shape": "S68"}, "FeatureSpecificationS3Uri": {}, "Mode": {}, "GenerateCandidateDefinitionsOnly": {"type": "boolean"}, "ProblemType": {}, "TargetAttributeName": {}, "SampleWeightAttributeName": {}}}, "TextGenerationJobConfig": {"type": "structure", "members": {"CompletionCriteria": {"shape": "S68"}, "BaseModelName": {}, "TextGenerationHyperParameters": {"type": "map", "key": {}, "value": {}}, "ModelAccessConfig": {"shape": "S13"}}}}, "union": true}, "S7p": {"type": "structure", "members": {"AlgorithmsConfig": {"shape": "S6j"}}}, "S7w": {"type": "structure", "members": {"EmrServerlessComputeConfig": {"type": "structure", "required": ["ExecutionRoleARN"], "members": {"ExecutionRoleARN": {}}}}}, "S81": {"type": "list", "member": {"type": "structure", "required": ["InstanceCount", "InstanceGroupName", "InstanceType", "LifeCycleConfig", "ExecutionRole"], "members": {"InstanceCount": {"type": "integer"}, "InstanceGroupName": {}, "InstanceType": {}, "LifeCycleConfig": {"shape": "S86"}, "ExecutionRole": {}, "ThreadsPerCore": {"type": "integer"}, "InstanceStorageConfigs": {"shape": "S8a"}}}}, "S86": {"type": "structure", "required": ["SourceS3Uri", "OnCreate"], "members": {"SourceS3Uri": {}, "OnCreate": {}}}, "S8a": {"type": "list", "member": {"type": "structure", "members": {"EbsVolumeConfig": {"type": "structure", "required": ["VolumeSizeInGB"], "members": {"VolumeSizeInGB": {"type": "integer"}}}}, "union": true}}, "S8h": {"type": "structure", "required": ["RepositoryUrl"], "members": {"RepositoryUrl": {}, "Branch": {}, "SecretArn": {}}}, "S8o": {"type": "structure", "required": ["S3Uri", "Framework"], "members": {"S3Uri": {}, "DataInputConfig": {}, "Framework": {}, "FrameworkVersion": {}}}, "S8r": {"type": "structure", "required": ["S3OutputLocation"], "members": {"S3OutputLocation": {}, "TargetDevice": {}, "TargetPlatform": {"type": "structure", "required": ["<PERSON><PERSON>", "Arch"], "members": {"Os": {}, "Arch": {}, "Accelerator": {}}}, "CompilerOptions": {}, "KmsKeyId": {}}}, "S8y": {"type": "structure", "required": ["SecurityGroupIds", "Subnets"], "members": {"SecurityGroupIds": {"type": "list", "member": {}}, "Subnets": {"type": "list", "member": {}}}}, "S97": {"type": "structure", "required": ["SourceUri"], "members": {"SourceUri": {}, "SourceType": {}, "SourceId": {}}}, "S9c": {"type": "structure", "members": {"BaseliningJobName": {}, "ConstraintsResource": {"shape": "S9e"}, "StatisticsResource": {"shape": "S9f"}}}, "S9e": {"type": "structure", "members": {"S3Uri": {}}}, "S9f": {"type": "structure", "members": {"S3Uri": {}}}, "S9g": {"type": "structure", "required": ["ImageUri"], "members": {"ImageUri": {}, "ContainerEntrypoint": {"shape": "S9i"}, "ContainerArguments": {"shape": "S9k"}, "RecordPreprocessorSourceUri": {}, "PostAnalyticsProcessorSourceUri": {}, "Environment": {"shape": "S9m"}}}, "S9i": {"type": "list", "member": {}}, "S9k": {"type": "list", "member": {}}, "S9m": {"type": "map", "key": {}, "value": {}}, "S9p": {"type": "structure", "members": {"EndpointInput": {"shape": "S9q"}, "BatchTransformInput": {"shape": "S9x"}}}, "S9q": {"type": "structure", "required": ["EndpointName", "LocalPath"], "members": {"EndpointName": {}, "LocalPath": {}, "S3InputMode": {}, "S3DataDistributionType": {}, "FeaturesAttribute": {}, "InferenceAttribute": {}, "ProbabilityAttribute": {}, "ProbabilityThresholdAttribute": {"type": "double"}, "StartTimeOffset": {}, "EndTimeOffset": {}, "ExcludeFeaturesAttribute": {}}}, "S9x": {"type": "structure", "required": ["DataCapturedDestinationS3Uri", "DatasetFormat", "LocalPath"], "members": {"DataCapturedDestinationS3Uri": {}, "DatasetFormat": {"type": "structure", "members": {"Csv": {"type": "structure", "members": {"Header": {"type": "boolean"}}}, "Json": {"type": "structure", "members": {"Line": {"type": "boolean"}}}, "Parquet": {"type": "structure", "members": {}}}}, "LocalPath": {}, "S3InputMode": {}, "S3DataDistributionType": {}, "FeaturesAttribute": {}, "InferenceAttribute": {}, "ProbabilityAttribute": {}, "ProbabilityThresholdAttribute": {"type": "double"}, "StartTimeOffset": {}, "EndTimeOffset": {}, "ExcludeFeaturesAttribute": {}}}, "Sa3": {"type": "structure", "required": ["MonitoringOutputs"], "members": {"MonitoringOutputs": {"type": "list", "member": {"type": "structure", "required": ["S3Output"], "members": {"S3Output": {"type": "structure", "required": ["S3Uri", "LocalPath"], "members": {"S3Uri": {}, "LocalPath": {}, "S3UploadMode": {}}}}}}, "KmsKeyId": {}}}, "Sa9": {"type": "structure", "required": ["ClusterConfig"], "members": {"ClusterConfig": {"type": "structure", "required": ["InstanceCount", "InstanceType", "VolumeSizeInGB"], "members": {"InstanceCount": {"type": "integer"}, "InstanceType": {}, "VolumeSizeInGB": {"type": "integer"}, "VolumeKmsKeyId": {}}}}}, "Sae": {"type": "structure", "members": {"EnableInterContainerTrafficEncryption": {"type": "boolean"}, "EnableNetworkIsolation": {"type": "boolean"}, "VpcConfig": {"shape": "S6d"}}}, "Saf": {"type": "structure", "required": ["MaxRuntimeInSeconds"], "members": {"MaxRuntimeInSeconds": {"type": "integer"}}}, "Sal": {"type": "structure", "required": ["S3OutputLocation"], "members": {"S3OutputLocation": {}, "KmsKeyId": {}, "PresetDeploymentType": {}, "PresetDeploymentConfig": {}}}, "Sar": {"type": "structure", "members": {"ExecutionRole": {}, "SecurityGroups": {"shape": "Sas"}, "SharingSettings": {"type": "structure", "members": {"NotebookOutputOption": {}, "S3OutputPath": {}, "S3KmsKeyId": {}}}, "JupyterServerAppSettings": {"shape": "Sav"}, "KernelGatewayAppSettings": {"shape": "Sb0"}, "TensorBoardAppSettings": {"type": "structure", "members": {"DefaultResourceSpec": {"shape": "S4r"}}}, "RStudioServerProAppSettings": {"type": "structure", "members": {"AccessStatus": {}, "UserGroup": {}}}, "RSessionAppSettings": {"type": "structure", "members": {"DefaultResourceSpec": {"shape": "S4r"}, "CustomImages": {"shape": "Sb1"}}}, "CanvasAppSettings": {"type": "structure", "members": {"TimeSeriesForecastingSettings": {"type": "structure", "members": {"Status": {}, "AmazonForecastRoleArn": {}}}, "ModelRegisterSettings": {"type": "structure", "members": {"Status": {}, "CrossAccountModelRegisterRoleArn": {}}}, "WorkspaceSettings": {"type": "structure", "members": {"S3ArtifactPath": {}, "S3KmsKeyId": {}}}, "IdentityProviderOAuthSettings": {"type": "list", "member": {"type": "structure", "members": {"DataSourceName": {}, "Status": {}, "SecretArn": {}}}}, "DirectDeploySettings": {"type": "structure", "members": {"Status": {}}}, "KendraSettings": {"type": "structure", "members": {"Status": {}}}, "GenerativeAiSettings": {"type": "structure", "members": {"AmazonBedrockRoleArn": {}}}, "EmrServerlessSettings": {"type": "structure", "members": {"ExecutionRoleArn": {}, "Status": {}}}}}, "CodeEditorAppSettings": {"type": "structure", "members": {"DefaultResourceSpec": {"shape": "S4r"}, "CustomImages": {"shape": "Sb1"}, "LifecycleConfigArns": {"shape": "<PERSON>"}, "AppLifecycleManagement": {"shape": "Sbn"}}}, "JupyterLabAppSettings": {"shape": "Sbr"}, "SpaceStorageSettings": {"shape": "Sbv"}, "DefaultLandingUri": {}, "StudioWebPortal": {}, "CustomPosixUserConfig": {"shape": "Sc0"}, "CustomFileSystemConfigs": {"shape": "Sc3"}, "StudioWebPortalSettings": {"type": "structure", "members": {"HiddenMlTools": {"type": "list", "member": {}}, "HiddenAppTypes": {"type": "list", "member": {}}}}, "AutoMountHomeEFS": {}}}, "Sas": {"type": "list", "member": {}}, "Sav": {"type": "structure", "members": {"DefaultResourceSpec": {"shape": "S4r"}, "LifecycleConfigArns": {"shape": "<PERSON>"}, "CodeRepositories": {"shape": "Sax"}}}, "Saw": {"type": "list", "member": {}}, "Sax": {"type": "list", "member": {"type": "structure", "required": ["RepositoryUrl"], "members": {"RepositoryUrl": {}}}}, "Sb0": {"type": "structure", "members": {"DefaultResourceSpec": {"shape": "S4r"}, "CustomImages": {"shape": "Sb1"}, "LifecycleConfigArns": {"shape": "<PERSON>"}}}, "Sb1": {"type": "list", "member": {"type": "structure", "required": ["ImageName", "AppImageConfigName"], "members": {"ImageName": {}, "ImageVersionNumber": {"type": "integer"}, "AppImageConfigName": {}}}}, "Sbn": {"type": "structure", "members": {"IdleSettings": {"type": "structure", "members": {"LifecycleManagement": {}, "IdleTimeoutInMinutes": {"type": "integer"}, "MinIdleTimeoutInMinutes": {"type": "integer"}, "MaxIdleTimeoutInMinutes": {"type": "integer"}}}}}, "Sbr": {"type": "structure", "members": {"DefaultResourceSpec": {"shape": "S4r"}, "CustomImages": {"shape": "Sb1"}, "LifecycleConfigArns": {"shape": "<PERSON>"}, "CodeRepositories": {"shape": "Sax"}, "AppLifecycleManagement": {"shape": "Sbn"}, "EmrSettings": {"type": "structure", "members": {"AssumableRoleArns": {"type": "list", "member": {}}, "ExecutionRoleArns": {"type": "list", "member": {}}}}}}, "Sbv": {"type": "structure", "members": {"DefaultEbsStorageSettings": {"type": "structure", "required": ["DefaultEbsVolumeSizeInGb", "MaximumEbsVolumeSizeInGb"], "members": {"DefaultEbsVolumeSizeInGb": {"type": "integer"}, "MaximumEbsVolumeSizeInGb": {"type": "integer"}}}}}, "Sc0": {"type": "structure", "required": ["<PERSON><PERSON>", "Gid"], "members": {"Uid": {"type": "long"}, "Gid": {"type": "long"}}}, "Sc3": {"type": "list", "member": {"type": "structure", "members": {"EFSFileSystemConfig": {"type": "structure", "required": ["FileSystemId"], "members": {"FileSystemId": {}, "FileSystemPath": {}}}}, "union": true}}, "Scc": {"type": "structure", "members": {"SecurityGroupIds": {"shape": "Scd"}, "RStudioServerProDomainSettings": {"type": "structure", "required": ["DomainExecutionRoleArn"], "members": {"DomainExecutionRoleArn": {}, "RStudioConnectUrl": {}, "RStudioPackageManagerUrl": {}, "DefaultResourceSpec": {"shape": "S4r"}}}, "ExecutionRoleIdentityConfig": {}, "DockerSettings": {"shape": "Scg"}, "AmazonQSettings": {"shape": "Scj"}}}, "Scd": {"type": "list", "member": {}}, "Scg": {"type": "structure", "members": {"EnableDockerAccess": {}, "VpcOnlyTrustedAccounts": {"type": "list", "member": {}}}}, "Scj": {"type": "structure", "members": {"Status": {}, "QProfileArn": {}}}, "Sco": {"type": "structure", "members": {"ExecutionRole": {}, "SecurityGroups": {"shape": "Sas"}, "JupyterServerAppSettings": {"shape": "Sav"}, "KernelGatewayAppSettings": {"shape": "Sb0"}, "JupyterLabAppSettings": {"shape": "Sbr"}, "SpaceStorageSettings": {"shape": "Sbv"}, "CustomPosixUserConfig": {"shape": "Sc0"}, "CustomFileSystemConfigs": {"shape": "Sc3"}}}, "Sct": {"type": "list", "member": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "EdgePackagingJobName"], "members": {"ModelHandle": {}, "EdgePackagingJobName": {}}}}, "Scv": {"type": "list", "member": {"type": "structure", "required": ["StageName", "DeviceSelectionConfig"], "members": {"StageName": {}, "DeviceSelectionConfig": {"shape": "Scx"}, "DeploymentConfig": {"shape": "Sd2"}}}}, "Scx": {"type": "structure", "required": ["DeviceSubsetType"], "members": {"DeviceSubsetType": {}, "Percentage": {"type": "integer"}, "DeviceNames": {"shape": "Sd0"}, "DeviceNameContains": {}}}, "Sd0": {"type": "list", "member": {}}, "Sd2": {"type": "structure", "required": ["FailureHandlingPolicy"], "members": {"FailureHandlingPolicy": {}}}, "Sdb": {"type": "structure", "members": {"BlueGreenUpdatePolicy": {"type": "structure", "required": ["TrafficRoutingConfiguration"], "members": {"TrafficRoutingConfiguration": {"type": "structure", "required": ["Type", "WaitIntervalInSeconds"], "members": {"Type": {}, "WaitIntervalInSeconds": {"type": "integer"}, "CanarySize": {"shape": "Sdg"}, "LinearStepSize": {"shape": "Sdg"}}}, "TerminationWaitInSeconds": {"type": "integer"}, "MaximumExecutionTimeoutInSeconds": {"type": "integer"}}}, "RollingUpdatePolicy": {"type": "structure", "required": ["MaximumBatchSize", "WaitIntervalInSeconds"], "members": {"MaximumBatchSize": {"shape": "Sdg"}, "WaitIntervalInSeconds": {"type": "integer"}, "MaximumExecutionTimeoutInSeconds": {"type": "integer"}, "RollbackMaximumBatchSize": {"shape": "Sdg"}}}, "AutoRollbackConfiguration": {"type": "structure", "members": {"Alarms": {"type": "list", "member": {"type": "structure", "members": {"AlarmName": {}}}}}}}}, "Sdg": {"type": "structure", "required": ["Type", "Value"], "members": {"Type": {}, "Value": {"type": "integer"}}}, "Sdt": {"type": "list", "member": {"type": "structure", "required": ["VariantName"], "members": {"VariantName": {}, "ModelName": {}, "InitialInstanceCount": {"type": "integer"}, "InstanceType": {}, "InitialVariantWeight": {"type": "float"}, "AcceleratorType": {}, "CoreDumpConfig": {"type": "structure", "required": ["DestinationS3Uri"], "members": {"DestinationS3Uri": {}, "KmsKeyId": {}}}, "ServerlessConfig": {"shape": "Se1"}, "VolumeSizeInGB": {"type": "integer"}, "ModelDataDownloadTimeoutInSeconds": {"type": "integer"}, "ContainerStartupHealthCheckTimeoutInSeconds": {"type": "integer"}, "EnableSSMAccess": {"type": "boolean"}, "ManagedInstanceScaling": {"shape": "Se9"}, "RoutingConfig": {"shape": "Sed"}, "InferenceAmiVersion": {}}}}, "Se1": {"type": "structure", "required": ["MemorySizeInMB", "MaxConcurrency"], "members": {"MemorySizeInMB": {"type": "integer"}, "MaxConcurrency": {"type": "integer"}, "ProvisionedConcurrency": {"type": "integer"}}}, "Se9": {"type": "structure", "members": {"Status": {}, "MinInstanceCount": {"type": "integer"}, "MaxInstanceCount": {"type": "integer"}}}, "Sed": {"type": "structure", "required": ["RoutingStrategy"], "members": {"RoutingStrategy": {}}}, "Seg": {"type": "structure", "required": ["InitialSamplingPercentage", "DestinationS3Uri", "CaptureOptions"], "members": {"EnableCapture": {"type": "boolean"}, "InitialSamplingPercentage": {"type": "integer"}, "DestinationS3Uri": {}, "KmsKeyId": {}, "CaptureOptions": {"type": "list", "member": {"type": "structure", "required": ["CaptureMode"], "members": {"CaptureMode": {}}}}, "CaptureContentTypeHeader": {"shape": "Se<PERSON>"}}}, "Sem": {"type": "structure", "members": {"CsvContentTypes": {"type": "list", "member": {}}, "JsonContentTypes": {"type": "list", "member": {}}}}, "Ser": {"type": "structure", "required": ["OutputConfig"], "members": {"ClientConfig": {"type": "structure", "members": {"MaxConcurrentInvocationsPerInstance": {"type": "integer"}}}, "OutputConfig": {"type": "structure", "members": {"KmsKeyId": {}, "S3OutputPath": {}, "NotificationConfig": {"type": "structure", "members": {"SuccessTopic": {}, "ErrorTopic": {}, "IncludeInferenceResponseIn": {"type": "list", "member": {}}}}, "S3FailurePath": {}}}}}, "Sez": {"type": "structure", "members": {"ClarifyExplainerConfig": {"type": "structure", "required": ["ShapConfig"], "members": {"EnableExplanations": {}, "InferenceConfig": {"type": "structure", "members": {"FeaturesAttribute": {}, "ContentTemplate": {}, "MaxRecordCount": {"type": "integer"}, "MaxPayloadInMB": {"type": "integer"}, "ProbabilityIndex": {"type": "integer"}, "LabelIndex": {"type": "integer"}, "ProbabilityAttribute": {}, "LabelAttribute": {}, "LabelHeaders": {"type": "list", "member": {}}, "FeatureHeaders": {"type": "list", "member": {}}, "FeatureTypes": {"type": "list", "member": {}}}}, "ShapConfig": {"type": "structure", "required": ["ShapBaselineConfig"], "members": {"ShapBaselineConfig": {"type": "structure", "members": {"MimeType": {}, "ShapBaseline": {}, "ShapBaselineUri": {}}}, "NumberOfSamples": {"type": "integer"}, "UseLogit": {"type": "boolean"}, "Seed": {"type": "integer"}, "TextConfig": {"type": "structure", "required": ["Language", "Granularity"], "members": {"Language": {}, "Granularity": {}}}}}}}}}, "Sfy": {"type": "list", "member": {"shape": "Sfz"}}, "Sfz": {"type": "structure", "required": ["FeatureName", "FeatureType"], "members": {"FeatureName": {}, "FeatureType": {}, "CollectionType": {}, "CollectionConfig": {"type": "structure", "members": {"VectorConfig": {"type": "structure", "required": ["Dimension"], "members": {"Dimension": {"type": "integer"}}}}, "union": true}}}, "Sg5": {"type": "structure", "members": {"SecurityConfig": {"type": "structure", "members": {"KmsKeyId": {}}}, "EnableOnlineStore": {"type": "boolean"}, "TtlDuration": {"shape": "Sg7"}, "StorageType": {}}}, "Sg7": {"type": "structure", "members": {"Unit": {}, "Value": {"type": "integer"}}}, "Sgb": {"type": "structure", "required": ["S3StorageConfig"], "members": {"S3StorageConfig": {"type": "structure", "required": ["S3Uri"], "members": {"S3Uri": {}, "KmsKeyId": {}, "ResolvedOutputS3Uri": {}}}, "DisableGlueTableCreation": {"type": "boolean"}, "DataCatalogConfig": {"type": "structure", "required": ["TableName", "Catalog", "Database"], "members": {"TableName": {}, "Catalog": {}, "Database": {}}}, "TableFormat": {}}}, "Sgq": {"type": "structure", "required": ["AwsManagedHumanLoopRequestSource"], "members": {"AwsManagedHumanLoopRequestSource": {}}}, "Sgs": {"type": "structure", "required": ["HumanLoopActivationConditionsConfig"], "members": {"HumanLoopActivationConditionsConfig": {"type": "structure", "required": ["HumanLoopActivationConditions"], "members": {"HumanLoopActivationConditions": {"jsonvalue": true}}}}}, "Sgv": {"type": "structure", "required": ["WorkteamArn", "HumanTaskUiArn", "TaskTitle", "TaskDescription", "TaskCount"], "members": {"WorkteamArn": {}, "HumanTaskUiArn": {}, "TaskTitle": {}, "TaskDescription": {}, "TaskCount": {"type": "integer"}, "TaskAvailabilityLifetimeInSeconds": {"type": "integer"}, "TaskTimeLimitInSeconds": {"type": "integer"}, "TaskKeywords": {"type": "list", "member": {}}, "PublicWorkforceTaskPrice": {"shape": "Sh5"}}}, "Sh5": {"type": "structure", "members": {"AmountInUsd": {"type": "structure", "members": {"Dollars": {"type": "integer"}, "Cents": {"type": "integer"}, "TenthFractionsOfACent": {"type": "integer"}}}}}, "Sha": {"type": "structure", "required": ["S3OutputPath"], "members": {"S3OutputPath": {}, "KmsKeyId": {}}}, "Shh": {"type": "list", "member": {}}, "Shj": {"type": "structure", "members": {"S3OutputPath": {}}}, "Shv": {"type": "structure", "required": ["Content"], "members": {"Content": {}}}, "Si0": {"type": "structure", "required": ["Strategy", "ResourceLimits"], "members": {"Strategy": {}, "StrategyConfig": {"type": "structure", "members": {"HyperbandStrategyConfig": {"type": "structure", "members": {"MinResource": {"type": "integer"}, "MaxResource": {"type": "integer"}}}}}, "HyperParameterTuningJobObjective": {"shape": "S2y"}, "ResourceLimits": {"shape": "Si6"}, "ParameterRanges": {"shape": "Sia"}, "TrainingJobEarlyStoppingType": {}, "TuningJobCompletionCriteria": {"type": "structure", "members": {"TargetObjectiveMetricValue": {"type": "float"}, "BestObjectiveNotImproving": {"type": "structure", "members": {"MaxNumberOfTrainingJobsNotImproving": {"type": "integer"}}}, "ConvergenceDetected": {"type": "structure", "members": {"CompleteOnConvergence": {}}}}}, "RandomSeed": {"type": "integer"}}}, "Si6": {"type": "structure", "required": ["MaxParallelTrainingJobs"], "members": {"MaxNumberOfTrainingJobs": {"type": "integer"}, "MaxParallelTrainingJobs": {"type": "integer"}, "MaxRuntimeInSeconds": {"type": "integer"}}}, "Sia": {"type": "structure", "members": {"IntegerParameterRanges": {"type": "list", "member": {"type": "structure", "required": ["Name", "MinValue", "MaxValue"], "members": {"Name": {}, "MinValue": {}, "MaxValue": {}, "ScalingType": {}}}}, "ContinuousParameterRanges": {"type": "list", "member": {"type": "structure", "required": ["Name", "MinValue", "MaxValue"], "members": {"Name": {}, "MinValue": {}, "MaxValue": {}, "ScalingType": {}}}}, "CategoricalParameterRanges": {"type": "list", "member": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {}, "Values": {"shape": "S2i"}}}}, "AutoParameters": {"type": "list", "member": {"type": "structure", "required": ["Name", "ValueHint"], "members": {"Name": {}, "ValueHint": {}}}}}}, "Sit": {"type": "structure", "required": ["AlgorithmSpecification", "RoleArn", "OutputDataConfig", "StoppingCondition"], "members": {"DefinitionName": {}, "TuningObjective": {"shape": "S2y"}, "HyperParameterRanges": {"shape": "Sia"}, "StaticHyperParameters": {"shape": "S35"}, "AlgorithmSpecification": {"type": "structure", "required": ["TrainingInputMode"], "members": {"TrainingImage": {}, "TrainingInputMode": {}, "AlgorithmName": {}, "MetricDefinitions": {"shape": "S2n"}}}, "RoleArn": {}, "InputDataConfig": {"shape": "S37"}, "VpcConfig": {"shape": "S6d"}, "OutputDataConfig": {"shape": "S3p"}, "ResourceConfig": {"shape": "S3s"}, "HyperParameterTuningResourceConfig": {"type": "structure", "members": {"InstanceType": {}, "InstanceCount": {"type": "integer"}, "VolumeSizeInGB": {"type": "integer"}, "VolumeKmsKeyId": {}, "AllocationStrategy": {}, "InstanceConfigs": {"type": "list", "member": {"type": "structure", "required": ["InstanceType", "InstanceCount", "VolumeSizeInGB"], "members": {"InstanceType": {}, "InstanceCount": {"type": "integer"}, "VolumeSizeInGB": {"type": "integer"}}}}}}, "StoppingCondition": {"shape": "S3y"}, "EnableNetworkIsolation": {"type": "boolean"}, "EnableInterContainerTrafficEncryption": {"type": "boolean"}, "EnableManagedSpotTraining": {"type": "boolean"}, "CheckpointConfig": {"shape": "Sj3"}, "RetryStrategy": {"shape": "Sj4"}, "Environment": {"type": "map", "key": {}, "value": {}}}}, "Sj3": {"type": "structure", "required": ["S3Uri"], "members": {"S3Uri": {}, "LocalPath": {}}}, "Sj4": {"type": "structure", "required": ["MaximumRetryAttempts"], "members": {"MaximumRetryAttempts": {"type": "integer"}}}, "Sj9": {"type": "list", "member": {"shape": "Sit"}}, "Sja": {"type": "structure", "required": ["ParentHyperParameterTuningJobs", "WarmStartType"], "members": {"ParentHyperParameterTuningJobs": {"type": "list", "member": {"type": "structure", "members": {"HyperParameterTuningJobName": {}}}}, "WarmStartType": {}}}, "Sje": {"type": "structure", "required": ["Mode"], "members": {"Mode": {}}}, "Sjp": {"type": "list", "member": {}}, "Sk1": {"type": "structure", "required": ["ComputeResourceRequirements"], "members": {"ModelName": {}, "Container": {"type": "structure", "members": {"Image": {}, "ArtifactUrl": {}, "Environment": {"shape": "S18"}}}, "StartupParameters": {"shape": "Sk3"}, "ComputeResourceRequirements": {"shape": "Sk4"}}}, "Sk3": {"type": "structure", "members": {"ModelDataDownloadTimeoutInSeconds": {"type": "integer"}, "ContainerStartupHealthCheckTimeoutInSeconds": {"type": "integer"}}}, "Sk4": {"type": "structure", "required": ["MinMemoryRequiredInMb"], "members": {"NumberOfCpuCoresRequired": {"type": "float"}, "NumberOfAcceleratorDevicesRequired": {"type": "float"}, "MinMemoryRequiredInMb": {"type": "integer"}, "MaxMemoryRequiredInMb": {"type": "integer"}}}, "Sk8": {"type": "structure", "required": ["CopyCount"], "members": {"CopyCount": {"type": "integer"}}}, "Skf": {"type": "structure", "members": {"StartTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}}}, "Ski": {"type": "list", "member": {"type": "structure", "required": ["ModelName", "VariantName", "InfrastructureConfig"], "members": {"ModelName": {}, "VariantName": {}, "InfrastructureConfig": {"shape": "Skl"}}}}, "Skl": {"type": "structure", "required": ["InfrastructureType", "RealTimeInferenceConfig"], "members": {"InfrastructureType": {}, "RealTimeInferenceConfig": {"type": "structure", "required": ["InstanceType", "InstanceCount"], "members": {"InstanceType": {}, "InstanceCount": {"type": "integer"}}}}}, "Skq": {"type": "structure", "required": ["Destination"], "members": {"Destination": {}, "KmsKey": {}, "ContentType": {"shape": "Se<PERSON>"}}}, "Skr": {"type": "structure", "required": ["SourceModelVariantName", "ShadowModelVariants"], "members": {"SourceModelVariantName": {}, "ShadowModelVariants": {"type": "list", "member": {"type": "structure", "required": ["ShadowModelVariantName", "SamplingPercentage"], "members": {"ShadowModelVariantName": {}, "SamplingPercentage": {"type": "integer"}}}}}}, "Skz": {"type": "structure", "members": {"ModelPackageVersionArn": {}, "ModelName": {}, "JobDurationInSeconds": {"type": "integer"}, "TrafficPattern": {"type": "structure", "members": {"TrafficType": {}, "Phases": {"type": "list", "member": {"type": "structure", "members": {"InitialNumberOfUsers": {"type": "integer"}, "SpawnRate": {"type": "integer"}, "DurationInSeconds": {"type": "integer"}}}}, "Stairs": {"type": "structure", "members": {"DurationInSeconds": {"type": "integer"}, "NumberOfSteps": {"type": "integer"}, "UsersPerStep": {"type": "integer"}}}}}, "ResourceLimit": {"type": "structure", "members": {"MaxNumberOfTests": {"type": "integer"}, "MaxParallelOfTests": {"type": "integer"}}}, "EndpointConfigurations": {"type": "list", "member": {"type": "structure", "members": {"InstanceType": {}, "ServerlessConfig": {"shape": "Se1"}, "InferenceSpecificationName": {}, "EnvironmentParameterRanges": {"type": "structure", "members": {"CategoricalParameterRanges": {"type": "list", "member": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {}, "Value": {"type": "list", "member": {}}}}}}}}}}, "VolumeKmsKeyId": {}, "ContainerConfig": {"type": "structure", "members": {"Domain": {}, "Task": {}, "Framework": {}, "FrameworkVersion": {}, "PayloadConfig": {"type": "structure", "members": {"SamplePayloadUrl": {}, "SupportedContentTypes": {"type": "list", "member": {}}}}, "NearestModelName": {}, "SupportedInstanceTypes": {"type": "list", "member": {}}, "SupportedEndpointType": {}, "DataInputConfig": {}, "SupportedResponseMIMETypes": {"type": "list", "member": {}}}}, "Endpoints": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "VpcConfig": {"type": "structure", "required": ["SecurityGroupIds", "Subnets"], "members": {"SecurityGroupIds": {"type": "list", "member": {}}, "Subnets": {"type": "list", "member": {}}}}}}, "Sly": {"type": "structure", "members": {"EndpointName": {}}}, "Sm5": {"type": "structure", "members": {"MaxInvocations": {"type": "integer"}, "ModelLatencyThresholds": {"type": "list", "member": {"type": "structure", "members": {"Percentile": {}, "ValueInMilliseconds": {"type": "integer"}}}}, "FlatInvocations": {}}}, "Smh": {"type": "structure", "required": ["DataSource"], "members": {"DataSource": {"type": "structure", "members": {"S3DataSource": {"type": "structure", "required": ["ManifestS3Uri"], "members": {"ManifestS3Uri": {}}}, "SnsDataSource": {"type": "structure", "required": ["SnsTopicArn"], "members": {"SnsTopicArn": {}}}}}, "DataAttributes": {"type": "structure", "members": {"ContentClassifiers": {"type": "list", "member": {}}}}}}, "Smo": {"type": "structure", "required": ["S3OutputPath"], "members": {"S3OutputPath": {}, "KmsKeyId": {}, "SnsTopicArn": {}}}, "Smp": {"type": "structure", "members": {"MaxHumanLabeledObjectCount": {"type": "integer"}, "MaxPercentageOfInputDatasetLabeled": {"type": "integer"}}}, "Sms": {"type": "structure", "required": ["LabelingJobAlgorithmSpecificationArn"], "members": {"LabelingJobAlgorithmSpecificationArn": {}, "InitialActiveLearningModelArn": {}, "LabelingJobResourceConfig": {"type": "structure", "members": {"VolumeKmsKeyId": {}, "VpcConfig": {"shape": "S6d"}}}}}, "Smw": {"type": "structure", "required": ["WorkteamArn", "UiConfig", "PreHumanTaskLambdaArn", "TaskTitle", "TaskDescription", "NumberOfHumanWorkersPerDataObject", "TaskTimeLimitInSeconds", "AnnotationConsolidationConfig"], "members": {"WorkteamArn": {}, "UiConfig": {"type": "structure", "members": {"UiTemplateS3Uri": {}, "HumanTaskUiArn": {}}}, "PreHumanTaskLambdaArn": {}, "TaskKeywords": {"type": "list", "member": {}}, "TaskTitle": {}, "TaskDescription": {}, "NumberOfHumanWorkersPerDataObject": {"type": "integer"}, "TaskTimeLimitInSeconds": {"type": "integer"}, "TaskAvailabilityLifetimeInSeconds": {"type": "integer"}, "MaxConcurrentTaskCount": {"type": "integer"}, "AnnotationConsolidationConfig": {"type": "structure", "required": ["AnnotationConsolidationLambdaArn"], "members": {"AnnotationConsolidationLambdaArn": {}}}, "PublicWorkforceTaskPrice": {"shape": "Sh5"}}}, "Sni": {"type": "structure", "members": {"ContainerHostname": {}, "Image": {}, "ImageConfig": {"type": "structure", "required": ["RepositoryAccessMode"], "members": {"RepositoryAccessMode": {}, "RepositoryAuthConfig": {"type": "structure", "required": ["RepositoryCredentialsProviderArn"], "members": {"RepositoryCredentialsProviderArn": {}}}}}, "Mode": {}, "ModelDataUrl": {}, "ModelDataSource": {"shape": "Sy"}, "AdditionalModelDataSources": {"type": "list", "member": {"type": "structure", "required": ["ChannelName", "S3DataSource"], "members": {"ChannelName": {}, "S3DataSource": {"shape": "Sz"}}}}, "Environment": {"shape": "S18"}, "ModelPackageName": {}, "InferenceSpecificationName": {}, "MultiModelConfig": {"type": "structure", "members": {"ModelCacheSetting": {}}}}}, "Snu": {"type": "list", "member": {"shape": "Sni"}}, "Snv": {"type": "structure", "required": ["Mode"], "members": {"Mode": {}}}, "Snz": {"type": "structure", "members": {"BaseliningJobName": {}, "ConstraintsResource": {"shape": "S9e"}}}, "So0": {"type": "structure", "required": ["ImageUri", "Config<PERSON><PERSON>"], "members": {"ImageUri": {}, "ConfigUri": {}, "Environment": {"shape": "S9m"}}}, "So1": {"type": "structure", "required": ["GroundTruthS3Input"], "members": {"EndpointInput": {"shape": "S9q"}, "BatchTransformInput": {"shape": "S9x"}, "GroundTruthS3Input": {"shape": "So2"}}}, "So2": {"type": "structure", "members": {"S3Uri": {}}}, "So5": {"type": "structure", "members": {"KmsKeyId": {}}}, "So6": {"type": "string", "sensitive": true}, "Soc": {"type": "structure", "required": ["S3OutputPath"], "members": {"S3OutputPath": {}}}, "Sog": {"type": "structure", "members": {"BaseliningJobName": {}, "ConstraintsResource": {"shape": "S9e"}}}, "Soh": {"type": "structure", "required": ["ImageUri", "Config<PERSON><PERSON>"], "members": {"ImageUri": {}, "ConfigUri": {}, "Environment": {"shape": "S9m"}}}, "Soi": {"type": "structure", "members": {"EndpointInput": {"shape": "S9q"}, "BatchTransformInput": {"shape": "S9x"}}}, "Sol": {"type": "structure", "required": ["ValidationRole", "ValidationProfiles"], "members": {"ValidationRole": {}, "ValidationProfiles": {"type": "list", "member": {"type": "structure", "required": ["ProfileName", "TransformJobDefinition"], "members": {"ProfileName": {}, "TransformJobDefinition": {"shape": "S42"}}}}}}, "Soo": {"type": "structure", "required": ["SourceAlgorithms"], "members": {"SourceAlgorithms": {"type": "list", "member": {"type": "structure", "required": ["AlgorithmName"], "members": {"ModelDataUrl": {}, "ModelDataSource": {"shape": "Sy"}, "AlgorithmName": {}}}}}}, "Sor": {"type": "structure", "members": {"ModelQuality": {"type": "structure", "members": {"Statistics": {"shape": "Sot"}, "Constraints": {"shape": "Sot"}}}, "ModelDataQuality": {"type": "structure", "members": {"Statistics": {"shape": "Sot"}, "Constraints": {"shape": "Sot"}}}, "Bias": {"type": "structure", "members": {"Report": {"shape": "Sot"}, "PreTrainingReport": {"shape": "Sot"}, "PostTrainingReport": {"shape": "Sot"}}}, "Explainability": {"type": "structure", "members": {"Report": {"shape": "Sot"}}}}}, "Sot": {"type": "structure", "required": ["ContentType", "S3Uri"], "members": {"ContentType": {}, "ContentDigest": {}, "S3Uri": {}}}, "Soy": {"type": "map", "key": {}, "value": {}}, "Sp1": {"type": "structure", "members": {"Bias": {"type": "structure", "members": {"ConfigFile": {"shape": "Sp3"}, "PreTrainingConstraints": {"shape": "Sot"}, "PostTrainingConstraints": {"shape": "Sot"}}}, "Explainability": {"type": "structure", "members": {"Constraints": {"shape": "Sot"}, "ConfigFile": {"shape": "Sp3"}}}, "ModelQuality": {"type": "structure", "members": {"Statistics": {"shape": "Sot"}, "Constraints": {"shape": "Sot"}}}, "ModelDataQuality": {"type": "structure", "members": {"Statistics": {"shape": "Sot"}, "Constraints": {"shape": "Sot"}}}}}, "Sp3": {"type": "structure", "required": ["S3Uri"], "members": {"ContentType": {}, "ContentDigest": {}, "S3Uri": {}}}, "Sp7": {"type": "list", "member": {"type": "structure", "required": ["Name", "Containers"], "members": {"Name": {}, "Description": {}, "Containers": {"shape": "Ss"}, "SupportedTransformInstanceTypes": {"shape": "S1j"}, "SupportedRealtimeInferenceInstanceTypes": {"shape": "S1l"}, "SupportedContentTypes": {"shape": "S1n"}, "SupportedResponseMIMETypes": {"shape": "S1p"}}}}, "Spb": {"type": "structure", "required": ["KmsKeyId"], "members": {"KmsKeyId": {}}}, "Spc": {"type": "structure", "members": {"ModelCardContent": {"shape": "So6"}, "ModelCardStatus": {}}}, "Spi": {"type": "structure", "members": {"BaseliningJobName": {}, "ConstraintsResource": {"shape": "S9e"}}}, "Spj": {"type": "structure", "required": ["ImageUri"], "members": {"ImageUri": {}, "ContainerEntrypoint": {"shape": "S9i"}, "ContainerArguments": {"shape": "S9k"}, "RecordPreprocessorSourceUri": {}, "PostAnalyticsProcessorSourceUri": {}, "ProblemType": {}, "Environment": {"shape": "S9m"}}}, "Spl": {"type": "structure", "required": ["GroundTruthS3Input"], "members": {"EndpointInput": {"shape": "S9q"}, "BatchTransformInput": {"shape": "S9x"}, "GroundTruthS3Input": {"shape": "So2"}}}, "Spp": {"type": "structure", "members": {"ScheduleConfig": {"type": "structure", "required": ["ScheduleExpression"], "members": {"ScheduleExpression": {}, "DataAnalysisStartTime": {}, "DataAnalysisEndTime": {}}}, "MonitoringJobDefinition": {"type": "structure", "required": ["MonitoringInputs", "MonitoringOutputConfig", "MonitoringResources", "MonitoringAppSpecification", "RoleArn"], "members": {"BaselineConfig": {"type": "structure", "members": {"BaseliningJobName": {}, "ConstraintsResource": {"shape": "S9e"}, "StatisticsResource": {"shape": "S9f"}}}, "MonitoringInputs": {"type": "list", "member": {"type": "structure", "members": {"EndpointInput": {"shape": "S9q"}, "BatchTransformInput": {"shape": "S9x"}}}}, "MonitoringOutputConfig": {"shape": "Sa3"}, "MonitoringResources": {"shape": "Sa9"}, "MonitoringAppSpecification": {"type": "structure", "required": ["ImageUri"], "members": {"ImageUri": {}, "ContainerEntrypoint": {"shape": "S9i"}, "ContainerArguments": {"shape": "S9k"}, "RecordPreprocessorSourceUri": {}, "PostAnalyticsProcessorSourceUri": {}}}, "StoppingCondition": {"shape": "<PERSON><PERSON>"}, "Environment": {"shape": "S9m"}, "NetworkConfig": {"shape": "Spx"}, "RoleArn": {}}}, "MonitoringJobDefinitionName": {}, "MonitoringType": {}}}, "Spx": {"type": "structure", "members": {"EnableInterContainerTrafficEncryption": {"type": "boolean"}, "EnableNetworkIsolation": {"type": "boolean"}, "VpcConfig": {"shape": "S6d"}}}, "Sq6": {"type": "list", "member": {}}, "Sq9": {"type": "list", "member": {}}, "Sqc": {"type": "structure", "required": ["MinimumInstanceMetadataServiceVersion"], "members": {"MinimumInstanceMetadataServiceVersion": {}}}, "Sqh": {"type": "list", "member": {"type": "structure", "members": {"Content": {}}}}, "Sqn": {"type": "structure", "members": {"S3": {"type": "structure", "members": {"S3Uri": {}, "ModelAccessConfig": {"type": "structure", "required": ["AcceptEula"], "members": {"AcceptEula": {"type": "boolean"}}}}}}}, "Sqs": {"type": "map", "key": {}, "value": {}}, "Sqt": {"type": "list", "member": {"type": "structure", "members": {"ModelQuantizationConfig": {"type": "structure", "members": {"Image": {}, "OverrideEnvironment": {"shape": "Sqs"}}}, "ModelCompilationConfig": {"type": "structure", "members": {"Image": {}, "OverrideEnvironment": {"shape": "Sqs"}}}}, "union": true}}, "Sqy": {"type": "structure", "required": ["S3OutputLocation"], "members": {"KmsKeyId": {}, "S3OutputLocation": {}}}, "Sqz": {"type": "structure", "required": ["SecurityGroupIds", "Subnets"], "members": {"SecurityGroupIds": {"type": "list", "member": {}}, "Subnets": {"type": "list", "member": {}}}}, "Sr9": {"type": "structure", "required": ["Bucket", "ObjectKey"], "members": {"Bucket": {}, "ObjectKey": {}, "VersionId": {}}}, "Srf": {"type": "structure", "required": ["MaxParallelExecutionSteps"], "members": {"MaxParallelExecutionSteps": {"type": "integer"}}}, "Srv": {"type": "list", "member": {"type": "structure", "required": ["InputName"], "members": {"InputName": {}, "AppManaged": {"type": "boolean"}, "S3Input": {"type": "structure", "required": ["S3Uri", "S3DataType"], "members": {"S3Uri": {}, "LocalPath": {}, "S3DataType": {}, "S3InputMode": {}, "S3DataDistributionType": {}, "S3CompressionType": {}}}, "DatasetDefinition": {"type": "structure", "members": {"AthenaDatasetDefinition": {"type": "structure", "required": ["Catalog", "Database", "QueryString", "OutputS3Uri", "OutputFormat"], "members": {"Catalog": {}, "Database": {}, "QueryString": {}, "WorkGroup": {}, "OutputS3Uri": {}, "KmsKeyId": {}, "OutputFormat": {}, "OutputCompression": {}}}, "RedshiftDatasetDefinition": {"type": "structure", "required": ["ClusterId", "Database", "DbUser", "QueryString", "ClusterRoleArn", "OutputS3Uri", "OutputFormat"], "members": {"ClusterId": {}, "Database": {}, "DbUser": {}, "QueryString": {}, "ClusterRoleArn": {}, "OutputS3Uri": {}, "KmsKeyId": {}, "OutputFormat": {}, "OutputCompression": {}}}, "LocalPath": {}, "DataDistributionType": {}, "InputMode": {}}}}}}, "Ssi": {"type": "structure", "required": ["Outputs"], "members": {"Outputs": {"type": "list", "member": {"type": "structure", "required": ["OutputName"], "members": {"OutputName": {}, "S3Output": {"type": "structure", "required": ["S3Uri", "S3UploadMode"], "members": {"S3Uri": {}, "LocalPath": {}, "S3UploadMode": {}}}, "FeatureStoreOutput": {"type": "structure", "required": ["FeatureGroupName"], "members": {"FeatureGroupName": {}}}, "AppManaged": {"type": "boolean"}}}}, "KmsKeyId": {}}}, "Ssn": {"type": "structure", "required": ["ClusterConfig"], "members": {"ClusterConfig": {"type": "structure", "required": ["InstanceCount", "InstanceType", "VolumeSizeInGB"], "members": {"InstanceCount": {"type": "integer"}, "InstanceType": {}, "VolumeSizeInGB": {"type": "integer"}, "VolumeKmsKeyId": {}}}}}, "Ssp": {"type": "structure", "required": ["MaxRuntimeInSeconds"], "members": {"MaxRuntimeInSeconds": {"type": "integer"}}}, "Ssr": {"type": "structure", "required": ["ImageUri"], "members": {"ImageUri": {}, "ContainerEntrypoint": {"shape": "S9i"}, "ContainerArguments": {"type": "list", "member": {}}}}, "Sst": {"type": "map", "key": {}, "value": {}}, "Ssu": {"type": "structure", "members": {"ExperimentName": {}, "TrialName": {}, "TrialComponentDisplayName": {}, "RunName": {}}}, "Ssz": {"type": "structure", "required": ["ProductId"], "members": {"ProductId": {}, "ProvisioningArtifactId": {}, "PathId": {}, "ProvisioningParameters": {"shape": "St1"}}}, "St1": {"type": "list", "member": {"type": "structure", "members": {"Key": {}, "Value": {}}}}, "St9": {"type": "structure", "members": {"JupyterServerAppSettings": {"shape": "Sav"}, "KernelGatewayAppSettings": {"shape": "Sb0"}, "CodeEditorAppSettings": {"type": "structure", "members": {"DefaultResourceSpec": {"shape": "S4r"}, "AppLifecycleManagement": {"shape": "Stb"}}}, "JupyterLabAppSettings": {"type": "structure", "members": {"DefaultResourceSpec": {"shape": "S4r"}, "CodeRepositories": {"shape": "Sax"}, "AppLifecycleManagement": {"shape": "Stb"}}}, "AppType": {}, "SpaceStorageSettings": {"shape": "Ste"}, "CustomFileSystems": {"type": "list", "member": {"type": "structure", "members": {"EFSFileSystem": {"type": "structure", "required": ["FileSystemId"], "members": {"FileSystemId": {}}}}, "union": true}}}}, "Stb": {"type": "structure", "members": {"IdleSettings": {"type": "structure", "members": {"IdleTimeoutInMinutes": {"type": "integer"}}}}}, "Ste": {"type": "structure", "members": {"EbsStorageSettings": {"type": "structure", "required": ["EbsVolumeSizeInGb"], "members": {"EbsVolumeSizeInGb": {"type": "integer"}}}}}, "Stj": {"type": "structure", "required": ["OwnerUserProfileName"], "members": {"OwnerUserProfileName": {}}}, "Stk": {"type": "structure", "required": ["SharingType"], "members": {"SharingType": {}}}, "Stv": {"type": "structure", "required": ["TrainingInputMode"], "members": {"TrainingImage": {}, "AlgorithmName": {}, "TrainingInputMode": {}, "MetricDefinitions": {"shape": "S2n"}, "EnableSageMakerMetricsTimeSeries": {"type": "boolean"}, "ContainerEntrypoint": {"type": "list", "member": {}}, "ContainerArguments": {"type": "list", "member": {}}, "TrainingImageConfig": {"type": "structure", "required": ["TrainingRepositoryAccessMode"], "members": {"TrainingRepositoryAccessMode": {}, "TrainingRepositoryAuthConfig": {"type": "structure", "required": ["TrainingRepositoryCredentialsProviderArn"], "members": {"TrainingRepositoryCredentialsProviderArn": {}}}}}}}, "Su4": {"type": "structure", "required": ["S3OutputPath"], "members": {"LocalPath": {}, "S3OutputPath": {}, "HookParameters": {"type": "map", "key": {}, "value": {}}, "CollectionConfigurations": {"type": "list", "member": {"type": "structure", "members": {"CollectionName": {}, "CollectionParameters": {"type": "map", "key": {}, "value": {}}}}}}}, "Suc": {"type": "list", "member": {"type": "structure", "required": ["RuleConfigurationName", "RuleEvaluatorImage"], "members": {"RuleConfigurationName": {}, "LocalPath": {}, "S3OutputPath": {}, "RuleEvaluatorImage": {}, "InstanceType": {}, "VolumeSizeInGB": {"type": "integer"}, "RuleParameters": {"shape": "<PERSON><PERSON>"}}}}, "Suf": {"type": "map", "key": {}, "value": {}}, "Sug": {"type": "structure", "required": ["S3OutputPath"], "members": {"LocalPath": {}, "S3OutputPath": {}}}, "Suh": {"type": "structure", "members": {"S3OutputPath": {}, "ProfilingIntervalInMilliseconds": {"type": "long"}, "ProfilingParameters": {"shape": "<PERSON><PERSON>"}, "DisableProfiler": {"type": "boolean"}}}, "Suj": {"type": "map", "key": {}, "value": {}}, "Sul": {"type": "list", "member": {"type": "structure", "required": ["RuleConfigurationName", "RuleEvaluatorImage"], "members": {"RuleConfigurationName": {}, "LocalPath": {}, "S3OutputPath": {}, "RuleEvaluatorImage": {}, "InstanceType": {}, "VolumeSizeInGB": {"type": "integer"}, "RuleParameters": {"shape": "<PERSON><PERSON>"}}}}, "Sun": {"type": "map", "key": {}, "value": {}}, "Suq": {"type": "structure", "members": {"EnableRemoteDebug": {"type": "boolean"}}}, "Sus": {"type": "structure", "members": {"EnableInfraCheck": {"type": "boolean"}}}, "Sv0": {"type": "structure", "members": {"InvocationsTimeoutInSeconds": {"type": "integer"}, "InvocationsMaxRetries": {"type": "integer"}}}, "Sv3": {"type": "structure", "required": ["DestinationS3Uri"], "members": {"DestinationS3Uri": {}, "KmsKeyId": {}, "GenerateInferenceId": {"type": "boolean"}}}, "Sv4": {"type": "structure", "members": {"InputFilter": {}, "OutputFilter": {}, "JoinSource": {}}}, "Svc": {"type": "structure", "members": {"PrimaryStatus": {}, "Message": {}}}, "Svf": {"type": "map", "key": {}, "value": {"type": "structure", "members": {"StringValue": {}, "NumberValue": {"type": "double"}}}}, "Svj": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["Value"], "members": {"MediaType": {}, "Value": {}}}}, "Svu": {"type": "structure", "required": ["UserPool", "ClientId"], "members": {"UserPool": {}, "ClientId": {}}}, "Svx": {"type": "structure", "required": ["ClientId", "ClientSecret", "Issuer", "AuthorizationEndpoint", "TokenEndpoint", "UserInfoEndpoint", "LogoutEndpoint", "JwksUri"], "members": {"ClientId": {}, "ClientSecret": {"type": "string", "sensitive": true}, "Issuer": {}, "AuthorizationEndpoint": {}, "TokenEndpoint": {}, "UserInfoEndpoint": {}, "LogoutEndpoint": {}, "JwksUri": {}, "Scope": {}, "AuthenticationRequestExtraParams": {"shape": "Sw1"}}}, "Sw1": {"type": "map", "key": {}, "value": {}}, "Sw4": {"type": "structure", "required": ["Cidrs"], "members": {"Cidrs": {"type": "list", "member": {}}}}, "Sw8": {"type": "structure", "members": {"VpcId": {}, "SecurityGroupIds": {"shape": "Swa"}, "Subnets": {"shape": "Swc"}}}, "Swa": {"type": "list", "member": {}}, "Swc": {"type": "list", "member": {}}, "Swi": {"type": "list", "member": {"type": "structure", "members": {"CognitoMemberDefinition": {"type": "structure", "required": ["UserPool", "UserGroup", "ClientId"], "members": {"UserPool": {}, "UserGroup": {}, "ClientId": {}}}, "OidcMemberDefinition": {"type": "structure", "members": {"Groups": {"type": "list", "member": {}}}}}}}, "Swq": {"type": "structure", "members": {"NotificationTopicArn": {}}}, "Sws": {"type": "structure", "members": {"S3Presign": {"type": "structure", "members": {"IamPolicyConstraints": {"type": "structure", "members": {"SourceIp": {}, "VpcSourceIp": {}}}}}}}, "Sz5": {"type": "structure", "members": {"UserProfileArn": {}, "UserProfileName": {}, "DomainId": {}, "IamIdentity": {"type": "structure", "members": {"Arn": {}, "PrincipalId": {}, "SourceIdentity": {}}}}}, "Szc": {"type": "list", "member": {"type": "structure", "required": ["Name", "Status"], "members": {"Name": {}, "Status": {}, "FailureReason": {}}}}, "Szq": {"type": "list", "member": {"type": "structure", "members": {"PartialFailureMessage": {}}}}, "Szs": {"type": "structure", "required": ["CandidateName", "ObjectiveStatus", "CandidateSteps", "CandidateS<PERSON>us", "CreationTime", "LastModifiedTime"], "members": {"CandidateName": {}, "FinalAutoMLJobObjectiveMetric": {"type": "structure", "required": ["MetricName", "Value"], "members": {"Type": {}, "MetricName": {}, "Value": {"type": "float"}, "StandardMetricName": {}}}, "ObjectiveStatus": {}, "CandidateSteps": {"type": "list", "member": {"type": "structure", "required": ["CandidateStepType", "CandidateStepArn", "CandidateStepName"], "members": {"CandidateStepType": {}, "CandidateStepArn": {}, "CandidateStepName": {}}}}, "CandidateStatus": {}, "InferenceContainers": {"shape": "S104"}, "CreationTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}, "CandidateProperties": {"type": "structure", "members": {"CandidateArtifactLocations": {"type": "structure", "required": ["Explainability"], "members": {"Explainability": {}, "ModelInsights": {}, "BacktestResults": {}}}, "CandidateMetrics": {"type": "list", "member": {"type": "structure", "members": {"MetricName": {}, "Value": {"type": "float"}, "Set": {}, "StandardMetricName": {}}}}}}, "InferenceContainerDefinitions": {"type": "map", "key": {}, "value": {"shape": "S104"}}}}, "S104": {"type": "list", "member": {"type": "structure", "required": ["Image", "ModelDataUrl"], "members": {"Image": {}, "ModelDataUrl": {}, "Environment": {"shape": "S18"}}}}, "S10k": {"type": "structure", "members": {"CandidateDefinitionNotebookLocation": {}, "DataExplorationNotebookLocation": {}}}, "S10o": {"type": "structure", "members": {"EndpointName": {}}}, "S116": {"type": "structure", "required": ["Status"], "members": {"Status": {}, "Message": {}}}, "S11k": {"type": "structure", "required": ["S3ModelArtifacts"], "members": {"S3ModelArtifacts": {}}}, "S12q": {"type": "list", "member": {"type": "structure", "required": ["VariantName"], "members": {"VariantName": {}, "DeployedImages": {"shape": "S12s"}, "CurrentWeight": {"type": "float"}, "DesiredWeight": {"type": "float"}, "CurrentInstanceCount": {"type": "integer"}, "DesiredInstanceCount": {"type": "integer"}, "VariantStatus": {"shape": "S12u"}, "CurrentServerlessConfig": {"shape": "Se1"}, "DesiredServerlessConfig": {"shape": "Se1"}, "ManagedInstanceScaling": {"shape": "Se9"}, "RoutingConfig": {"shape": "Sed"}}}}, "S12s": {"type": "list", "member": {"shape": "S12t"}}, "S12t": {"type": "structure", "members": {"SpecifiedImage": {}, "ResolvedImage": {}, "ResolutionTime": {"type": "timestamp"}}}, "S12u": {"type": "list", "member": {"type": "structure", "required": ["Status"], "members": {"Status": {}, "StatusMessage": {}, "StartTime": {"type": "timestamp"}}}}, "S12y": {"type": "structure", "required": ["EnableCapture", "CaptureStatus", "CurrentSamplingPercentage", "DestinationS3Uri", "KmsKeyId"], "members": {"EnableCapture": {"type": "boolean"}, "CaptureStatus": {}, "CurrentSamplingPercentage": {"type": "integer"}, "DestinationS3Uri": {}, "KmsKeyId": {}}}, "S132": {"type": "list", "member": {"type": "structure", "required": ["VariantName"], "members": {"VariantName": {}, "DeployedImages": {"shape": "S12s"}, "CurrentWeight": {"type": "float"}, "DesiredWeight": {"type": "float"}, "CurrentInstanceCount": {"type": "integer"}, "DesiredInstanceCount": {"type": "integer"}, "InstanceType": {}, "AcceleratorType": {}, "VariantStatus": {"shape": "S12u"}, "CurrentServerlessConfig": {"shape": "Se1"}, "DesiredServerlessConfig": {"shape": "Se1"}, "ManagedInstanceScaling": {"shape": "Se9"}, "RoutingConfig": {"shape": "Sed"}}}}, "S138": {"type": "structure", "required": ["SourceArn"], "members": {"SourceArn": {}, "SourceType": {}}}, "S13g": {"type": "structure", "required": ["Status"], "members": {"Status": {}, "BlockedReason": {}}}, "S13j": {"type": "structure", "required": ["Status"], "members": {"Status": {}, "FailureReason": {}}}, "S13p": {"type": "list", "member": {"shape": "S13q"}}, "S13q": {"type": "structure", "members": {"Key": {}, "Value": {}}}, "S148": {"type": "list", "member": {}}, "S14n": {"type": "structure", "members": {"Completed": {"type": "integer"}, "InProgress": {"type": "integer"}, "RetryableError": {"type": "integer"}, "NonRetryableError": {"type": "integer"}, "Stopped": {"type": "integer"}}}, "S14p": {"type": "structure", "members": {"Succeeded": {"type": "integer"}, "Pending": {"type": "integer"}, "Failed": {"type": "integer"}}}, "S14r": {"type": "structure", "required": ["TrainingJobName", "TrainingJobArn", "CreationTime", "TrainingJobStatus", "TunedHyperParameters"], "members": {"TrainingJobDefinitionName": {}, "TrainingJobName": {}, "TrainingJobArn": {}, "TuningJobName": {}, "CreationTime": {"type": "timestamp"}, "TrainingStartTime": {"type": "timestamp"}, "TrainingEndTime": {"type": "timestamp"}, "TrainingJobStatus": {}, "TunedHyperParameters": {"shape": "S35"}, "FailureReason": {}, "FinalHyperParameterTuningJobObjectiveMetric": {"type": "structure", "required": ["MetricName", "Value"], "members": {"Type": {}, "MetricName": {}, "Value": {"type": "float"}}}, "ObjectiveStatus": {}}}, "S14u": {"type": "structure", "members": {"NumberOfTrainingJobsObjectiveNotImproving": {"type": "integer"}, "ConvergenceDetectedTime": {"type": "timestamp"}}}, "S14v": {"type": "structure", "members": {"RuntimeInSeconds": {"type": "integer"}}}, "S15m": {"type": "structure", "members": {"CostPerHour": {"type": "float"}, "CostPerInference": {"type": "float"}, "MaxInvocations": {"type": "integer"}, "ModelLatency": {"type": "integer"}, "CpuUtilization": {"type": "float"}, "MemoryUtilization": {"type": "float"}, "ModelSetupTime": {"type": "integer"}}}, "S15p": {"type": "structure", "required": ["EndpointName", "VariantName"], "members": {"EndpointName": {}, "VariantName": {}, "InstanceType": {}, "InitialInstanceCount": {"type": "integer"}, "ServerlessConfig": {"shape": "Se1"}}}, "S15r": {"type": "structure", "members": {"InferenceSpecificationName": {}, "EnvironmentParameters": {"type": "list", "member": {"type": "structure", "required": ["Key", "ValueType", "Value"], "members": {"Key": {}, "ValueType": {}, "Value": {}}}}, "CompilationJobName": {}}}, "S15z": {"type": "structure", "required": ["MaxInvocations", "ModelLatency"], "members": {"MaxInvocations": {"type": "integer"}, "ModelLatency": {"type": "integer"}}}, "S163": {"type": "structure", "members": {"TotalLabeled": {"type": "integer"}, "HumanLabeled": {"type": "integer"}, "MachineLabeled": {"type": "integer"}, "FailedNonRetryableError": {"type": "integer"}, "Unlabeled": {"type": "integer"}}}, "S166": {"type": "structure", "required": ["OutputDatasetS3Uri"], "members": {"OutputDatasetS3Uri": {}, "FinalActiveLearningModelArn": {}}}, "S16f": {"type": "structure", "required": ["RecommendationStatus"], "members": {"RecommendationStatus": {}, "RealTimeInferenceRecommendations": {"type": "list", "member": {"type": "structure", "required": ["RecommendationId", "InstanceType"], "members": {"RecommendationId": {}, "InstanceType": {}, "Environment": {"shape": "S18"}}}}}}, "S16w": {"type": "structure", "required": ["ValidationStatuses"], "members": {"ValidationStatuses": {"shape": "S16x"}, "ImageScanStatuses": {"shape": "S16x"}}}, "S16x": {"type": "list", "member": {"type": "structure", "required": ["Name", "Status"], "members": {"Name": {}, "Status": {}, "FailureReason": {}}}}, "S179": {"type": "structure", "required": ["MonitoringScheduleName", "ScheduledTime", "CreationTime", "LastModifiedTime", "MonitoringExecutionStatus"], "members": {"MonitoringScheduleName": {}, "ScheduledTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "MonitoringExecutionStatus": {}, "ProcessingJobArn": {}, "EndpointName": {}, "FailureReason": {}, "MonitoringJobDefinitionName": {}, "MonitoringType": {}}}, "S17x": {"type": "structure", "members": {"ExperimentName": {}, "TrialName": {}}}, "S17z": {"type": "structure", "required": ["SelectedSteps"], "members": {"SourcePipelineExecutionArn": {}, "SelectedSteps": {"type": "list", "member": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"StepName": {}}}}}}, "S188": {"type": "structure", "members": {"ProvisionedProductId": {}, "ProvisionedProductStatusMessage": {}}}, "S18j": {"type": "structure", "required": ["WorkteamArn"], "members": {"WorkteamArn": {}, "MarketplaceTitle": {}, "SellerName": {}, "MarketplaceDescription": {}, "ListingId": {}}}, "S18n": {"type": "structure", "required": ["Status"], "members": {"Status": {}, "ResourceRetainedBillableTimeInSeconds": {"type": "integer"}, "ReusedByJob": {}}}, "S18q": {"type": "list", "member": {"type": "structure", "required": ["Status", "StartTime"], "members": {"Status": {}, "StartTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "StatusMessage": {}}}}, "S18t": {"type": "list", "member": {"type": "structure", "members": {"MetricName": {}, "Value": {"type": "float"}, "Timestamp": {"type": "timestamp"}}}}, "S18x": {"type": "list", "member": {"type": "structure", "members": {"RuleConfigurationName": {}, "RuleEvaluationJobArn": {}, "RuleEvaluationStatus": {}, "StatusDetails": {}, "LastModifiedTime": {"type": "timestamp"}}}}, "S199": {"type": "structure", "required": ["SourceArn"], "members": {"SourceArn": {}, "SourceType": {}}}, "S19d": {"type": "structure", "required": ["SourceArn"], "members": {"SourceArn": {}, "SourceType": {}}}, "S19f": {"type": "list", "member": {"type": "structure", "members": {"MetricName": {}, "SourceArn": {}, "TimeStamp": {"type": "timestamp"}, "Max": {"type": "double"}, "Min": {"type": "double"}, "Last": {"type": "double"}, "Count": {"type": "integer"}, "Avg": {"type": "double"}, "StdDev": {"type": "double"}}}}, "S19p": {"type": "structure", "required": ["WorkforceName", "WorkforceArn"], "members": {"WorkforceName": {}, "WorkforceArn": {}, "LastUpdatedDate": {"type": "timestamp"}, "SourceIpConfig": {"shape": "Sw4"}, "SubDomain": {}, "CognitoConfig": {"shape": "Svu"}, "OidcConfig": {"type": "structure", "members": {"ClientId": {}, "Issuer": {}, "AuthorizationEndpoint": {}, "TokenEndpoint": {}, "UserInfoEndpoint": {}, "LogoutEndpoint": {}, "JwksUri": {}, "Scope": {}, "AuthenticationRequestExtraParams": {"shape": "Sw1"}}}, "CreateDate": {"type": "timestamp"}, "WorkforceVpcConfig": {"type": "structure", "required": ["VpcId", "SecurityGroupIds", "Subnets"], "members": {"VpcId": {}, "SecurityGroupIds": {"shape": "Swa"}, "Subnets": {"shape": "Swc"}, "VpcEndpointId": {}}}, "Status": {}, "FailureReason": {}}}, "S19x": {"type": "structure", "required": ["WorkteamName", "MemberDefinitions", "WorkteamArn", "Description"], "members": {"WorkteamName": {}, "MemberDefinitions": {"shape": "Swi"}, "WorkteamArn": {}, "WorkforceArn": {}, "ProductListingIds": {"type": "list", "member": {}}, "Description": {}, "SubDomain": {}, "CreateDate": {"type": "timestamp"}, "LastUpdatedDate": {"type": "timestamp"}, "NotificationConfiguration": {"shape": "Swq"}, "WorkerAccessConfiguration": {"shape": "Sws"}}}, "S1ap": {"type": "structure", "members": {"MinInvocationsPerMinute": {"type": "integer"}, "MaxInvocationsPerMinute": {"type": "integer"}}}, "S1dh": {"type": "list", "member": {"type": "structure", "required": ["MonitoringJobDefinitionName", "MonitoringJobDefinitionArn", "CreationTime", "EndpointName"], "members": {"MonitoringJobDefinitionName": {}, "MonitoringJobDefinitionArn": {}, "CreationTime": {"type": "timestamp"}, "EndpointName": {}}}}, "S1f7": {"type": "list", "member": {"type": "structure", "required": ["HubContentName", "HubContentArn", "HubContentVersion", "HubContentType", "DocumentSchemaVersion", "HubContentStatus", "CreationTime"], "members": {"HubContentName": {}, "HubContentArn": {}, "SageMakerPublicHubContentArn": {}, "HubContentVersion": {}, "HubContentType": {}, "DocumentSchemaVersion": {}, "HubContentDisplayName": {}, "HubContentDescription": {}, "SupportStatus": {}, "HubContentSearchKeywords": {"shape": "S148"}, "HubContentStatus": {}, "CreationTime": {"type": "timestamp"}, "OriginalCreationTime": {"type": "timestamp"}}}}, "S1ix": {"type": "list", "member": {"type": "structure", "required": ["MonitoringAlertName", "CreationTime", "LastModifiedTime", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DatapointsToAlert", "EvaluationPeriod", "Actions"], "members": {"MonitoringAlertName": {}, "CreationTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "AlertStatus": {}, "DatapointsToAlert": {"type": "integer"}, "EvaluationPeriod": {"type": "integer"}, "Actions": {"type": "structure", "members": {"ModelDashboardIndicator": {"type": "structure", "members": {"Enabled": {"type": "boolean"}}}}}}}}, "S1ki": {"type": "list", "member": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {}, "Value": {}}}}, "S1l1": {"type": "list", "member": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {}, "Value": {}}}}, "S1nz": {"type": "list", "member": {"type": "structure", "required": ["DeviceName"], "members": {"DeviceName": {}, "Description": {}, "IotThingName": {}}}}, "S1oa": {"type": "structure", "members": {"Filters": {"shape": "S1ob"}, "NestedFilters": {"type": "list", "member": {"type": "structure", "required": ["NestedPropertyName", "Filters"], "members": {"NestedPropertyName": {}, "Filters": {"shape": "S1ob"}}}}, "SubExpressions": {"type": "list", "member": {"shape": "S1oa"}}, "Operator": {}}}, "S1ob": {"type": "list", "member": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Operator": {}, "Value": {}}}}, "S1or": {"type": "structure", "members": {"TrainingJobName": {}, "TrainingJobArn": {}, "TuningJobArn": {}, "LabelingJobArn": {}, "AutoMLJobArn": {}, "ModelArtifacts": {"shape": "S11k"}, "TrainingJobStatus": {}, "SecondaryStatus": {}, "FailureReason": {}, "HyperParameters": {"shape": "S35"}, "AlgorithmSpecification": {"shape": "Stv"}, "RoleArn": {}, "InputDataConfig": {"shape": "S37"}, "OutputDataConfig": {"shape": "S3p"}, "ResourceConfig": {"shape": "S3s"}, "VpcConfig": {"shape": "S6d"}, "StoppingCondition": {"shape": "S3y"}, "CreationTime": {"type": "timestamp"}, "TrainingStartTime": {"type": "timestamp"}, "TrainingEndTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "SecondaryStatusTransitions": {"shape": "S18q"}, "FinalMetricDataList": {"shape": "S18t"}, "EnableNetworkIsolation": {"type": "boolean"}, "EnableInterContainerTrafficEncryption": {"type": "boolean"}, "EnableManagedSpotTraining": {"type": "boolean"}, "CheckpointConfig": {"shape": "Sj3"}, "TrainingTimeInSeconds": {"type": "integer"}, "BillableTimeInSeconds": {"type": "integer"}, "DebugHookConfig": {"shape": "Su4"}, "ExperimentConfig": {"shape": "Ssu"}, "DebugRuleConfigurations": {"shape": "Suc"}, "TensorBoardOutputConfig": {"shape": "<PERSON><PERSON>"}, "DebugRuleEvaluationStatuses": {"shape": "S18x"}, "ProfilerConfig": {"shape": "<PERSON><PERSON>"}, "Environment": {"shape": "Sun"}, "RetryStrategy": {"shape": "Sj4"}, "Tags": {"shape": "S7"}}}, "S1oz": {"type": "structure", "members": {"TransformJobName": {}, "TransformJobArn": {}, "TransformJobStatus": {}, "FailureReason": {}, "ModelName": {}, "MaxConcurrentTransforms": {"type": "integer"}, "ModelClientConfig": {"shape": "Sv0"}, "MaxPayloadInMB": {"type": "integer"}, "BatchStrategy": {}, "Environment": {"shape": "S46"}, "TransformInput": {"shape": "S49"}, "TransformOutput": {"shape": "S4d"}, "DataCaptureConfig": {"shape": "Sv3"}, "TransformResources": {"shape": "S4g"}, "CreationTime": {"type": "timestamp"}, "TransformStartTime": {"type": "timestamp"}, "TransformEndTime": {"type": "timestamp"}, "LabelingJobArn": {}, "AutoMLJobArn": {}, "DataProcessing": {"shape": "Sv4"}, "ExperimentConfig": {"shape": "Ssu"}, "Tags": {"shape": "S7"}}}, "S1ql": {"type": "list", "member": {}}, "S1t4": {"type": "list", "member": {}}}}