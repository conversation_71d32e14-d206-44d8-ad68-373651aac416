{"pagination": {"GetExclusionsPreview": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAssessmentRunAgents": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAssessmentRuns": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAssessmentTargets": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAssessmentTemplates": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListEventSubscriptions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListExclusions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListFindings": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListRulesPackages": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "PreviewAgents": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}}}