{"pagination": {"GetBlueprintRuns": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetClassifiers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetColumnStatisticsTaskRuns": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetConnections": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetCrawlerMetrics": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetCrawlers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetDatabases": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetDevEndpoints": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetJobRuns": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "JobRuns"}, "GetJobs": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Jobs"}, "GetMLTaskRuns": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetMLTransforms": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetPartitionIndexes": {"input_token": "NextToken", "output_token": "NextToken", "result_key": "PartitionIndexDescriptorList"}, "GetPartitions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetResourcePolicies": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "GetResourcePoliciesResponseList"}, "GetSecurityConfigurations": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "SecurityConfigurations"}, "GetTableVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetTables": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetTriggers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Triggers"}, "GetUnfilteredPartitionsMetadata": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetUserDefinedFunctions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetWorkflowRuns": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Runs"}, "ListBlueprints": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Blueprints"}, "ListColumnStatisticsTaskRuns": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListCrawlers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListCustomEntityTypes": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListDataQualityResults": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListDataQualityRuleRecommendationRuns": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListDataQualityRulesetEvaluationRuns": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListDataQualityRulesets": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListDevEndpoints": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListJobs": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "JobNames"}, "ListMLTransforms": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListRegistries": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Registries"}, "ListSchemaVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "<PERSON><PERSON><PERSON>"}, "ListSchemas": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "<PERSON><PERSON><PERSON>"}, "ListSessions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListTableOptimizerRuns": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListTriggers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "TriggerNames"}, "ListUsageProfiles": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Profiles"}, "ListWorkflows": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Workflows"}, "SearchTables": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}}}