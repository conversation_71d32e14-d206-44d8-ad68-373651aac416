{"name": "agent-alex", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "backend:dev": "cd backend && uvicorn main:app --reload --host 0.0.0.0 --port 8000", "backend:start": "cd backend && uvicorn main:app --host 0.0.0.0 --port 8000"}, "dependencies": {"aws-sdk": "^2.1500.0", "next": "^15.3.3", "react": "^18", "react-dom": "^18", "axios": "^1.6.0", "dotenv": "^16.3.1", "cors": "^2.8.5", "express": "^4.18.2"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.0.0", "nodemon": "^3.0.1"}}