# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# AWS Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
BEDROCK_REGION=us-east-1

# AWS Lambda Functions
LAMBDA_AI_CHAT_FUNCTION=aiChatHandler
LAMBDA_DASHBOARD_FUNCTION=getDashboardStats

# DynamoDB Tables
DYNAMODB_USERS_TABLE=agent-alex-users
DYNAMODB_CONVERSATIONS_TABLE=agent-alex-conversations
DYNAMODB_ANALYTICS_TABLE=agent-alex-analytics

# S3 Buckets
S3_UPLOADS_BUCKET=agent-alex-uploads
S3_MODELS_BUCKET=agent-alex-models

# Application Settings
NODE_ENV=development
NEXT_PUBLIC_APP_NAME=Agent Alex
NEXT_PUBLIC_API_URL=http://localhost:3000/api
